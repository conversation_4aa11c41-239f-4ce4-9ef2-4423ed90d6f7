验证码页面元素：
页面标题
文本: "Security Verification"
类型: heading
定位方法: await page.GetByRole("heading", new() { Name = "Security Verification" })

验证码图片
文本: "captcha"
类型: image
定位方法: await page.GetByRole("img", new() { Name = "captcha" })

验证码输入框
文本: "Verification answer"
类型: textbox
定位方法: await page.GetByLabel("Type the characters as shown above")

提交按钮
文本: "Submit"
类型: button
定位方法: await page.GetByRole("button", new() { Name = "Submit" })

重置按钮
文本: "Reset"
类型: button
定位方法: await page.GetByRole("button", new() { Name = "Reset" })

关闭按钮
文本: ""
类型: button
定位方法: await page.GetByRole("button", new() { Name = "Close" })

播放声音按钮
文本: ""
类型: button
定位方法: await page.GetByRole("button", new() { Name = "Play audio" })

刷新验证码按钮
文本: ""
类型: button
定位方法: await page.GetByRole("button", new() { Name = "Refresh" })

提示文本
文本: "Type the characters as shown above"
类型: text
定位方法: await page.GetByText("Type the characters as shown above")


错误提示元素信息：
错误提示文本
文本: "That wasn't quite right, please try again."
类型: text
定位方法: await page.GetByText("That wasn't quite right, please try again.")

错误提示图标容器
文本: ""
类型: generic
定位方法: await page.Locator("generic").Filter(new() { HasText = "That wasn't quite right, please try again." })





进入AWS管理控制台链接
文本: "Go to the AWS Management Console"
类型: link
定位方法: await page.GetByRole("link", new() { Name = "Go to the AWS Management Console" })




文本: "更多"
Lebih lanjut
类型: button
定位方法: await page.GetByRole(AriaRole.Button, new() { Name = "更多" })
TestId定位: await page.GetByTestId("awsc-nav-more-menu")

账户信息按钮
Akun
文本: "<EMAIL>"
类型: button
定位方法: await page.GetByRole("button", new() { Name = "<EMAIL>" })
文本: 账户邮箱 (例如 "<EMAIL>")
类型: button
定位方法: await page.GetByTestId("more-menu__awsc-nav-account-menu-button")
// 使用 Role 定位时需要邮箱
var accountButton = _page.GetByRole(AriaRole.Button, new() { Name = _currentData?.Email ?? "" });
// 使用 TestId 定位时不需要邮箱
var accountButton = _page.GetByTestId("more-menu__awsc-nav-account-menu-button");

区域选择按钮
文本: "区域 亚太地区 (悉尼)"
类型: combobox
定位方法: await page.GetByRole("combobox", new() { Name = "Regions (亚太地区 (悉尼))" })

设置按钮
文本: "设置"
类型: button
定位方法: await page.GetByRole("button", new() { Name = "设置" })


账户链接
Akun
文本: "账户"
类型: link
定位方法: await page.GetByRole("link", new() { Name = "账户" })

组织链接
文本: "组织"
类型: link
定位方法: await page.GetByRole("link", new() { Name = "组织" })

Service Quotas链接
文本: "Service Quotas"
类型: link
定位方法: await page.GetByRole("link", new() { Name = "Service Quotas" })

账单与成本管理链接
文本: "账单与成本管理"
类型: link
定位方法: await page.GetByRole("link", new() { Name = "账单与成本管理" })

安全凭证链接
Kredensial keamanan
文本: "安全凭证"
类型: link
定位方法: await page.GetByRole("link", new() { Name = "安全凭证" })
文本: "安全凭证"
类型: menuitem (菜单项)
定位方法: await page.GetByRole(AriaRole.Menuitem, new() { Name = "安全凭证" })
URL链接: https://ap-southeast-2.console.aws.amazon.com/iam/home?region=ap-southeast-2#security_credential



页面标题
文本: "IAM 用户和角色访问账单信息的权限"
类型: heading
定位方法: await page.GetByRole("heading", new() { Name = "IAM 用户和角色访问账单信息的权限" })

编辑按钮
Edit
文本: "编辑"
类型: button
定位方法: await page.GetByRole("button", new() { Name = "编辑" })

Akses pengguna dan peran IAM ke Informasi penagihan

激活 IAM 访问权限复选框
Aktifkan Akses IAM
文本: "激活 IAM 访问权限"
类型: checkbox
定位方法: await page.GetByLabel("激活 IAM 访问权限")

更新按钮
Perbarui
文本: "更新"
类型: button
定位方法: await page.GetByRole("button", new() { Name = "更新" })





文本: "AWS Console - Signup"
类型: title
定位方法: await page.Title

页面标题
文本: "Complete sign-up"
类型: heading
定位方法: await page.GetByRole("heading", new() { Name = "Complete sign-up" })

提示文本
文本: "Thanks for signing up for Amazon Web Services. If we have directed you to this page, then you have not finished registering. Make sure you have done the following:"
类型: paragraph
定位方法: await page.GetByText("Thanks for signing up for Amazon Web Services...")

完成注册链接
文本: "Complete your AWS registration"
类型: link
定位方法: await page.GetByRole("link", new() { Name = "Complete your AWS registration" })

联系支持链接
文本: "contact support"
类型: link
定位方法: await page.GetByRole("link", new() { Name = "contact support" })

信用卡信息链接
文本: "credit card information"
类型: link
定位方法: await page.GetByRole("link", new() { Name = "credit card information" })




创建访问密钥按钮
Buat access key
文本: "创建访问密钥"
类型: button
定位方法: await page.GetByRole("button", new() { Name = "创建访问密钥" })

确认复选框
Saya memahami bahwa membuat root access key bukanlah praktik terbaik, namun saya tetap ingin membuatnya.
文本: "我知道创建根访问密钥不是最佳实践，但我仍然想创建一个。"
类型: checkbox
定位方法: await page.GetByLabel("我知道创建根访问密钥不是最佳实践，但我仍然想创建一个。")

创建访问密钥确认按钮
Buat access key
文本: "创建访问密钥"
类型: button
定位方法: await page.GetByRole("button", new() { Name = "创建访问密钥" })


Access key   Secret access key

访问密钥复制按钮
文本: "复制访问密钥"
类型: button
定位方法: await page.GetByRole("cell", new() { Name = "********************" }).GetByTestId("copy-button")

显示秘密访问密钥按钮
文本: "显示"
类型: button
定位方法: await page.GetByTestId("show-password-link")

秘密访问密钥复制按钮
文本: "复制秘密访问密钥"
类型: button
定位方法: await page.GetByRole("cell", new() { Name = "771XSvtt0JEM9JrfCvYYHPzs2TPd9BkxwRmecbS3 隐藏" }).GetByTestId("copy-button")

已完成按钮
文本: "已完成"
类型: button
定位方法: await page.GetByRole("button", new() { Name = "已完成" })


继续按钮
Lanjutkan
文本: "继续"
类型: button
定位方法: await page.GetByRole("button", new() { Name = "继续" })



文本: "分配 MFA 设备"
类型: button
定位方法: await page.GetByRole("button", new() { Name = "分配 MFA 设备" })
TestId定位: await page.GetByTestId("user-mfa-assign-btn")



文本: "设备名称"
类型: textbox
定位方法: await page.GetByRole("textbox", new() { Name = "设备名称" })

文本: "通行密钥或安全密钥"
类型: radio
定位方法: await page.GetByRole("radio", new() { Name = "通行密钥或安全密钥" })

文本: "身份验证器应用程序"
类型: radio
定位方法: await page.GetByRole("radio", new() { Name = "身份验证器应用程序" })

文本: "硬件 TOTP 令牌"
类型: radio
定位方法: await page.GetByRole("radio", new() { Name = "硬件 TOTP 令牌" })

文本: "取消"
类型: button
定位方法: await page.GetByRole("button", new() { Name = "取消" })

文本: "下一步"
类型: button
定位方法: await page.GetByRole("button", new() { Name = "下一步" })



文本: "显示 QR 代码"
类型: button
定位方法: await page.GetByRole("button", new() { Name = "显示 QR 代码" })

文本: "显示密钥"
类型: link
定位方法: await page.GetByRole("link", new() { Name = "显示密钥" })

文本: "MFA 代码 1"
类型: textbox
定位方法: await page.GetByRole("textbox", new() { Name = "MFA 代码 1" })

文本: "MFA 代码 2"
类型: textbox
定位方法: await page.GetByRole("textbox", new() { Name = "MFA 代码 2" })

文本: "取消"
类型: button
定位方法: await page.GetByRole("button", new() { Name = "取消" })

文本: "上一步"
类型: button
定位方法: await page.GetByRole("button", new() { Name = "上一步" })

文本: "添加 MFA"
类型: button
定位方法: await page.GetByRole("button", new() { Name = "添加 MFA" })