using System;
using System.Collections.Generic;
using System.Linq;

namespace AWSAutoRegister.Services
{
    /// <summary>
    /// 国家代码到语言代码映射服务
    /// </summary>
    public static class CountryLanguageService
    {
        // 国家代码到语言代码的映射表（ISO 639-1语言代码）
        private static readonly Dictionary<string, string> CountryToLanguageMapping = new()
        {
            // 英语国家
            ["US"] = "en-US",    // 美国
            ["GB"] = "en-GB",    // 英国
            ["CA"] = "en-CA",    // 加拿大
            ["AU"] = "en-AU",    // 澳大利亚
            ["NZ"] = "en-NZ",    // 新西兰
            ["IE"] = "en-IE",    // 爱尔兰
            ["ZA"] = "en-ZA",    // 南非

            // 西班牙语国家
            ["ES"] = "es-ES",    // 西班牙
            ["MX"] = "es-MX",    // 墨西哥
            ["AR"] = "es-AR",    // 阿根廷
            ["CL"] = "es-CL",    // 智利
            ["CO"] = "es-CO",    // 哥伦比亚
            ["PE"] = "es-PE",    // 秘鲁
            ["VE"] = "es-VE",    // 委内瑞拉
            ["EC"] = "es-EC",    // 厄瓜多尔
            ["BO"] = "es-BO",    // 玻利维亚
            ["PY"] = "es-PY",    // 巴拉圭
            ["UY"] = "es-UY",    // 乌拉圭
            ["CR"] = "es-CR",    // 哥斯达黎加
            ["PA"] = "es-PA",    // 巴拿马
            ["GT"] = "es-GT",    // 危地马拉
            ["HN"] = "es-HN",    // 洪都拉斯
            ["SV"] = "es-SV",    // 萨尔瓦多
            ["NI"] = "es-NI",    // 尼加拉瓜
            ["DO"] = "es-DO",    // 多米尼加
            ["CU"] = "es-CU",    // 古巴

            // 法语国家
            ["FR"] = "fr-FR",    // 法国
            ["BE"] = "fr-BE",    // 比利时（法语区）
            ["CH"] = "fr-CH",    // 瑞士（法语区）
            ["LU"] = "fr-LU",    // 卢森堡
            ["MC"] = "fr-MC",    // 摩纳哥

            // 德语国家
            ["DE"] = "de-DE",    // 德国
            ["AT"] = "de-AT",    // 奥地利
            ["LI"] = "de-LI",    // 列支敦士登

            // 意大利语国家
            ["IT"] = "it-IT",    // 意大利
            ["SM"] = "it-SM",    // 圣马力诺
            ["VA"] = "it-VA",    // 梵蒂冈

            // 葡萄牙语国家
            ["PT"] = "pt-PT",    // 葡萄牙
            ["BR"] = "pt-BR",    // 巴西

            // 荷兰语国家
            ["NL"] = "nl-NL",    // 荷兰

            // 北欧语言
            ["SE"] = "sv-SE",    // 瑞典
            ["NO"] = "nb-NO",    // 挪威
            ["DK"] = "da-DK",    // 丹麦
            ["FI"] = "fi-FI",    // 芬兰
            ["IS"] = "is-IS",    // 冰岛

            // 东欧语言
            ["PL"] = "pl-PL",    // 波兰
            ["CZ"] = "cs-CZ",    // 捷克
            ["SK"] = "sk-SK",    // 斯洛伐克
            ["HU"] = "hu-HU",    // 匈牙利
            ["RO"] = "ro-RO",    // 罗马尼亚
            ["BG"] = "bg-BG",    // 保加利亚
            ["HR"] = "hr-HR",    // 克罗地亚
            ["SI"] = "sl-SI",    // 斯洛文尼亚
            ["EE"] = "et-EE",    // 爱沙尼亚
            ["LV"] = "lv-LV",    // 拉脱维亚
            ["LT"] = "lt-LT",    // 立陶宛

            // 俄语国家
            ["RU"] = "ru-RU",    // 俄罗斯
            ["BY"] = "ru-BY",    // 白俄罗斯
            ["KZ"] = "ru-KZ",    // 哈萨克斯坦

            // 亚洲语言
            ["CN"] = "zh-CN",    // 中国（简体中文）
            ["TW"] = "zh-TW",    // 台湾（繁体中文）
            ["HK"] = "zh-HK",    // 香港（繁体中文）
            ["JP"] = "ja-JP",    // 日本
            ["KR"] = "ko-KR",    // 韩国
            ["TH"] = "th-TH",    // 泰国
            ["VN"] = "vi-VN",    // 越南
            ["ID"] = "id-ID",    // 印度尼西亚
            ["MY"] = "ms-MY",    // 马来西亚
            ["SG"] = "en-SG",    // 新加坡（英语）
            ["PH"] = "en-PH",    // 菲律宾（英语）
            ["IN"] = "hi-IN",    // 印度（印地语）

            // 阿拉伯语国家
            ["SA"] = "ar-SA",    // 沙特阿拉伯
            ["AE"] = "ar-AE",    // 阿联酋
            ["EG"] = "ar-EG",    // 埃及
            ["JO"] = "ar-JO",    // 约旦
            ["LB"] = "ar-LB",    // 黎巴嫩
            ["SY"] = "ar-SY",    // 叙利亚
            ["IQ"] = "ar-IQ",    // 伊拉克
            ["KW"] = "ar-KW",    // 科威特
            ["QA"] = "ar-QA",    // 卡塔尔
            ["BH"] = "ar-BH",    // 巴林
            ["OM"] = "ar-OM",    // 阿曼
            ["YE"] = "ar-YE",    // 也门
            ["MA"] = "ar-MA",    // 摩洛哥
            ["TN"] = "ar-TN",    // 突尼斯
            ["DZ"] = "ar-DZ",    // 阿尔及利亚
            ["LY"] = "ar-LY",    // 利比亚

            // 土耳其语
            ["TR"] = "tr-TR",    // 土耳其

            // 希腊语
            ["GR"] = "el-GR",    // 希腊

            // 希伯来语
            ["IL"] = "he-IL",    // 以色列

            // 波斯语
            ["IR"] = "fa-IR",    // 伊朗

            // 其他语言
            ["UA"] = "uk-UA",    // 乌克兰
            ["RS"] = "sr-RS",    // 塞尔维亚
            ["BA"] = "bs-BA",    // 波斯尼亚
            ["MK"] = "mk-MK",    // 北马其顿
            ["AL"] = "sq-AL",    // 阿尔巴尼亚
            ["MT"] = "mt-MT",    // 马耳他
            ["CY"] = "el-CY",    // 塞浦路斯（希腊语）
        };

        // 国家代码到时区的映射表
        private static readonly Dictionary<string, string[]> CountryToTimezoneMapping = new()
        {
            // 北美洲
            ["US"] = new[] { "America/New_York", "America/Chicago", "America/Denver", "America/Los_Angeles" },
            ["CA"] = new[] { "America/Toronto", "America/Vancouver", "America/Montreal", "Canada/Pacific" },
            ["MX"] = new[] { "America/Mexico_City", "America/Tijuana", "America/Cancun" },

            // 欧洲
            ["GB"] = new[] { "Europe/London" },
            ["FR"] = new[] { "Europe/Paris" },
            ["DE"] = new[] { "Europe/Berlin" },
            ["IT"] = new[] { "Europe/Rome" },
            ["ES"] = new[] { "Europe/Madrid" },
            ["PT"] = new[] { "Europe/Lisbon" },
            ["NL"] = new[] { "Europe/Amsterdam" },
            ["BE"] = new[] { "Europe/Brussels" },
            ["CH"] = new[] { "Europe/Zurich" },
            ["AT"] = new[] { "Europe/Vienna" },
            ["SE"] = new[] { "Europe/Stockholm" },
            ["NO"] = new[] { "Europe/Oslo" },
            ["DK"] = new[] { "Europe/Copenhagen" },
            ["FI"] = new[] { "Europe/Helsinki" },
            ["PL"] = new[] { "Europe/Warsaw" },
            ["CZ"] = new[] { "Europe/Prague" },
            ["HU"] = new[] { "Europe/Budapest" },
            ["RO"] = new[] { "Europe/Bucharest" },
            ["BG"] = new[] { "Europe/Sofia" },
            ["GR"] = new[] { "Europe/Athens" },
            ["RU"] = new[] { "Europe/Moscow", "Asia/Yekaterinburg", "Asia/Novosibirsk" },
            ["UA"] = new[] { "Europe/Kiev" },
            ["TR"] = new[] { "Europe/Istanbul" },

            // 亚洲
            ["CN"] = new[] { "Asia/Shanghai" },
            ["JP"] = new[] { "Asia/Tokyo" },
            ["KR"] = new[] { "Asia/Seoul" },
            ["TH"] = new[] { "Asia/Bangkok" },
            ["VN"] = new[] { "Asia/Ho_Chi_Minh" },
            ["ID"] = new[] { "Asia/Jakarta", "Asia/Makassar" },
            ["MY"] = new[] { "Asia/Kuala_Lumpur" },
            ["SG"] = new[] { "Asia/Singapore" },
            ["PH"] = new[] { "Asia/Manila" },
            ["IN"] = new[] { "Asia/Kolkata" },
            ["HK"] = new[] { "Asia/Hong_Kong" },
            ["TW"] = new[] { "Asia/Taipei" },

            // 中东
            ["SA"] = new[] { "Asia/Riyadh" },
            ["AE"] = new[] { "Asia/Dubai" },
            ["IL"] = new[] { "Asia/Jerusalem" },
            ["IR"] = new[] { "Asia/Tehran" },
            ["EG"] = new[] { "Africa/Cairo" },
            ["JO"] = new[] { "Asia/Amman" },
            ["LB"] = new[] { "Asia/Beirut" },
            ["KW"] = new[] { "Asia/Kuwait" },
            ["QA"] = new[] { "Asia/Qatar" },
            ["BH"] = new[] { "Asia/Bahrain" },
            ["OM"] = new[] { "Asia/Muscat" },

            // 大洋洲
            ["AU"] = new[] { "Australia/Sydney", "Australia/Melbourne", "Australia/Perth" },
            ["NZ"] = new[] { "Pacific/Auckland" },

            // 南美洲
            ["BR"] = new[] { "America/Sao_Paulo", "America/Manaus", "America/Fortaleza" },
            ["AR"] = new[] { "America/Argentina/Buenos_Aires" },
            ["CL"] = new[] { "America/Santiago" },
            ["CO"] = new[] { "America/Bogota" },
            ["PE"] = new[] { "America/Lima" },
            ["VE"] = new[] { "America/Caracas" },
            ["EC"] = new[] { "America/Guayaquil" },
            ["UY"] = new[] { "America/Montevideo" },

            // 非洲
            ["ZA"] = new[] { "Africa/Johannesburg" },
            ["MA"] = new[] { "Africa/Casablanca" },
            ["TN"] = new[] { "Africa/Tunis" },
            ["DZ"] = new[] { "Africa/Algiers" },
            ["LY"] = new[] { "Africa/Tripoli" }
        };

        // 国家代码到地理位置的映射表（主要城市的经纬度）
        private static readonly Dictionary<string, (double Latitude, double Longitude)> CountryToGeolocationMapping = new()
        {
            // 北美洲
            ["US"] = (39.8283, -98.5795),    // 美国中心
            ["CA"] = (56.1304, -106.3468),   // 加拿大中心
            ["MX"] = (23.6345, -102.5528),   // 墨西哥中心

            // 欧洲
            ["GB"] = (55.3781, -3.4360),     // 英国中心
            ["FR"] = (46.2276, 2.2137),     // 法国中心
            ["DE"] = (51.1657, 10.4515),    // 德国中心
            ["IT"] = (41.8719, 12.5674),    // 意大利中心
            ["ES"] = (40.4637, -3.7492),    // 西班牙中心
            ["PT"] = (39.3999, -8.2245),    // 葡萄牙中心
            ["NL"] = (52.1326, 5.2913),     // 荷兰中心
            ["BE"] = (50.5039, 4.4699),     // 比利时中心
            ["CH"] = (46.8182, 8.2275),     // 瑞士中心
            ["AT"] = (47.5162, 14.5501),    // 奥地利中心
            ["SE"] = (60.1282, 18.6435),    // 瑞典中心
            ["NO"] = (60.4720, 8.4689),     // 挪威中心
            ["DK"] = (56.2639, 9.5018),     // 丹麦中心
            ["FI"] = (61.9241, 25.7482),    // 芬兰中心
            ["PL"] = (51.9194, 19.1451),    // 波兰中心
            ["CZ"] = (49.8175, 15.4730),    // 捷克中心
            ["HU"] = (47.1625, 19.5033),    // 匈牙利中心
            ["RO"] = (45.9432, 24.9668),    // 罗马尼亚中心
            ["BG"] = (42.7339, 25.4858),    // 保加利亚中心
            ["GR"] = (39.0742, 21.8243),    // 希腊中心
            ["RU"] = (61.5240, 105.3188),   // 俄罗斯中心
            ["UA"] = (48.3794, 31.1656),    // 乌克兰中心
            ["TR"] = (38.9637, 35.2433),    // 土耳其中心

            // 亚洲
            ["CN"] = (35.8617, 104.1954),   // 中国中心
            ["JP"] = (36.2048, 138.2529),   // 日本中心
            ["KR"] = (35.9078, 127.7669),   // 韩国中心
            ["TH"] = (15.8700, 100.9925),   // 泰国中心
            ["VN"] = (14.0583, 108.2772),   // 越南中心
            ["ID"] = (-0.7893, 113.9213),   // 印度尼西亚中心
            ["MY"] = (4.2105, 101.9758),    // 马来西亚中心
            ["SG"] = (1.3521, 103.8198),    // 新加坡
            ["PH"] = (12.8797, 121.7740),   // 菲律宾中心
            ["IN"] = (20.5937, 78.9629),    // 印度中心
            ["HK"] = (22.3193, 114.1694),   // 香港
            ["TW"] = (23.6978, 120.9605),   // 台湾中心

            // 中东
            ["SA"] = (23.8859, 45.0792),    // 沙特阿拉伯中心
            ["AE"] = (23.4241, 53.8478),    // 阿联酋中心
            ["IL"] = (31.0461, 34.8516),    // 以色列中心
            ["IR"] = (32.4279, 53.6880),    // 伊朗中心
            ["EG"] = (26.0975, 31.2357),    // 埃及中心
            ["JO"] = (30.5852, 36.2384),    // 约旦中心
            ["LB"] = (33.8547, 35.8623),    // 黎巴嫩中心
            ["KW"] = (29.3117, 47.4818),    // 科威特中心
            ["QA"] = (25.3548, 51.1839),    // 卡塔尔中心
            ["BH"] = (25.9304, 50.6378),    // 巴林中心
            ["OM"] = (21.4735, 55.9754),    // 阿曼中心

            // 大洋洲
            ["AU"] = (-25.2744, 133.7751),  // 澳大利亚中心
            ["NZ"] = (-40.9006, 174.8860),  // 新西兰中心

            // 南美洲
            ["BR"] = (-14.2350, -51.9253),  // 巴西中心
            ["AR"] = (-38.4161, -63.6167),  // 阿根廷中心
            ["CL"] = (-35.6751, -71.5430),  // 智利中心
            ["CO"] = (4.5709, -74.2973),    // 哥伦比亚中心
            ["PE"] = (-9.1900, -75.0152),   // 秘鲁中心
            ["VE"] = (6.4238, -66.5897),    // 委内瑞拉中心
            ["EC"] = (-1.8312, -78.1834),   // 厄瓜多尔中心
            ["UY"] = (-32.5228, -55.7658),  // 乌拉圭中心

            // 非洲
            ["ZA"] = (-30.5595, 22.9375),   // 南非中心
            ["MA"] = (31.7917, -7.0926),    // 摩洛哥中心
            ["TN"] = (33.8869, 9.5375),     // 突尼斯中心
            ["DZ"] = (28.0339, 1.6596),     // 阿尔及利亚中心
            ["LY"] = (26.3351, 17.2283)     // 利比亚中心
        };

        /// <summary>
        /// 根据国家代码获取对应的语言代码
        /// </summary>
        /// <param name="countryCode">国家代码（如US、CN、JP等）</param>
        /// <returns>语言代码，如果找不到则返回"en-US"作为默认值</returns>
        public static string GetLanguageCode(string countryCode)
        {
            if (string.IsNullOrEmpty(countryCode))
                return "en-US";

            var code = countryCode.ToUpper().Trim();
            return CountryToLanguageMapping.TryGetValue(code, out var languageCode) ? languageCode : "en-US";
        }

        /// <summary>
        /// 根据国家代码获取Chrome浏览器的语言参数
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <returns>Chrome语言参数</returns>
        public static string GetChromeLanguageArg(string countryCode)
        {
            var languageCode = GetLanguageCode(countryCode);
            return $"--lang={languageCode}";
        }

        /// <summary>
        /// 根据国家代码获取Accept-Language头部值
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <returns>Accept-Language头部值</returns>
        public static string GetAcceptLanguageHeader(string countryCode)
        {
            var languageCode = GetLanguageCode(countryCode);
            
            // 构建Accept-Language头部，包含主语言和备选语言
            var primaryLang = languageCode.Split('-')[0];
            if (primaryLang == "en")
            {
                return $"{languageCode},en;q=0.9";
            }
            else
            {
                return $"{languageCode},{primaryLang};q=0.9,en;q=0.8";
            }
        }

        /// <summary>
        /// 检查国家代码是否有对应的语言映射
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <returns>如果有映射返回true，否则返回false</returns>
        public static bool HasLanguageMapping(string countryCode)
        {
            if (string.IsNullOrEmpty(countryCode))
                return false;

            return CountryToLanguageMapping.ContainsKey(countryCode.ToUpper().Trim());
        }

        /// <summary>
        /// 获取所有支持的国家代码和对应的语言代码
        /// </summary>
        /// <returns>国家代码和语言代码的字典</returns>
        public static Dictionary<string, string> GetAllLanguageMappings()
        {
            return new Dictionary<string, string>(CountryToLanguageMapping);
        }

        /// <summary>
        /// 根据国家代码获取对应的时区
        /// </summary>
        /// <param name="countryCode">国家代码（如US、CN、JP等）</param>
        /// <returns>时区字符串，如果找不到则返回"America/New_York"作为默认值</returns>
        public static string GetTimezone(string countryCode)
        {
            if (string.IsNullOrEmpty(countryCode))
                return "America/New_York";

            var code = countryCode.ToUpper().Trim();
            if (CountryToTimezoneMapping.TryGetValue(code, out var timezones))
            {
                // 如果有多个时区，随机选择一个
                var random = new Random();
                return timezones[random.Next(timezones.Length)];
            }

            return "America/New_York"; // 默认时区
        }

        /// <summary>
        /// 根据国家代码获取所有可能的时区
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <returns>时区数组，如果找不到则返回默认时区数组</returns>
        public static string[] GetAllTimezones(string countryCode)
        {
            if (string.IsNullOrEmpty(countryCode))
                return new[] { "America/New_York" };

            var code = countryCode.ToUpper().Trim();
            return CountryToTimezoneMapping.TryGetValue(code, out var timezones)
                ? timezones
                : new[] { "America/New_York" };
        }

        /// <summary>
        /// 检查国家代码是否有对应的时区映射
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <returns>如果有映射返回true，否则返回false</returns>
        public static bool HasTimezoneMapping(string countryCode)
        {
            if (string.IsNullOrEmpty(countryCode))
                return false;

            return CountryToTimezoneMapping.ContainsKey(countryCode.ToUpper().Trim());
        }

        /// <summary>
        /// 根据国家代码获取对应的地理位置
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <returns>地理位置（纬度，经度），如果找不到则返回null</returns>
        public static (double Latitude, double Longitude)? GetGeolocation(string countryCode)
        {
            if (string.IsNullOrEmpty(countryCode))
                return null;

            var code = countryCode.ToUpper().Trim();
            return CountryToGeolocationMapping.TryGetValue(code, out var location)
                ? location
                : null;
        }

        /// <summary>
        /// 检查国家代码是否有对应的地理位置映射
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <returns>如果有映射返回true，否则返回false</returns>
        public static bool HasGeolocationMapping(string countryCode)
        {
            if (string.IsNullOrEmpty(countryCode))
                return false;

            return CountryToGeolocationMapping.ContainsKey(countryCode.ToUpper().Trim());
        }

        /// <summary>
        /// 根据国家代码获取语言的显示名称
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <returns>语言显示名称</returns>
        public static string GetLanguageDisplayName(string countryCode)
        {
            var languageCode = GetLanguageCode(countryCode);
            
            return languageCode switch
            {
                "en-US" => "English (United States)",
                "en-GB" => "English (United Kingdom)",
                "zh-CN" => "中文 (简体)",
                "zh-TW" => "中文 (繁體)",
                "ja-JP" => "日本語",
                "ko-KR" => "한국어",
                "es-ES" => "Español (España)",
                "es-MX" => "Español (México)",
                "fr-FR" => "Français",
                "de-DE" => "Deutsch",
                "it-IT" => "Italiano",
                "pt-BR" => "Português (Brasil)",
                "pt-PT" => "Português (Portugal)",
                "ru-RU" => "Русский",
                "ar-SA" => "العربية",
                "th-TH" => "ไทย",
                "tr-TR" => "Türkçe",
                "hi-IN" => "हिन्दी",
                "vi-VN" => "Tiếng Việt",
                "id-ID" => "Bahasa Indonesia",
                "ms-MY" => "Bahasa Melayu",
                _ => languageCode
            };
        }
    }
}
