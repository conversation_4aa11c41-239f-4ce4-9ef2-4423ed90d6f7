using System;
using System.Collections.Generic;
using System.Linq;

namespace AWSAutoRegister.Services
{
    /// <summary>
    /// 国家代码到语言代码映射服务
    /// </summary>
    public static class CountryLanguageService
    {
        // 国家代码到语言代码的映射表（ISO 639-1语言代码）
        private static readonly Dictionary<string, string> CountryToLanguageMapping = new()
        {
            // 英语国家
            ["US"] = "en-US",    // 美国
            ["GB"] = "en-GB",    // 英国
            ["CA"] = "en-CA",    // 加拿大
            ["AU"] = "en-AU",    // 澳大利亚
            ["NZ"] = "en-NZ",    // 新西兰
            ["IE"] = "en-IE",    // 爱尔兰
            ["ZA"] = "en-ZA",    // 南非

            // 西班牙语国家
            ["ES"] = "es-ES",    // 西班牙
            ["MX"] = "es-MX",    // 墨西哥
            ["AR"] = "es-AR",    // 阿根廷
            ["CL"] = "es-CL",    // 智利
            ["CO"] = "es-CO",    // 哥伦比亚
            ["PE"] = "es-PE",    // 秘鲁
            ["VE"] = "es-VE",    // 委内瑞拉
            ["EC"] = "es-EC",    // 厄瓜多尔
            ["BO"] = "es-BO",    // 玻利维亚
            ["PY"] = "es-PY",    // 巴拉圭
            ["UY"] = "es-UY",    // 乌拉圭
            ["CR"] = "es-CR",    // 哥斯达黎加
            ["PA"] = "es-PA",    // 巴拿马
            ["GT"] = "es-GT",    // 危地马拉
            ["HN"] = "es-HN",    // 洪都拉斯
            ["SV"] = "es-SV",    // 萨尔瓦多
            ["NI"] = "es-NI",    // 尼加拉瓜
            ["DO"] = "es-DO",    // 多米尼加
            ["CU"] = "es-CU",    // 古巴

            // 法语国家
            ["FR"] = "fr-FR",    // 法国
            ["BE"] = "fr-BE",    // 比利时（法语区）
            ["CH"] = "fr-CH",    // 瑞士（法语区）
            ["LU"] = "fr-LU",    // 卢森堡
            ["MC"] = "fr-MC",    // 摩纳哥

            // 德语国家
            ["DE"] = "de-DE",    // 德国
            ["AT"] = "de-AT",    // 奥地利
            ["LI"] = "de-LI",    // 列支敦士登

            // 意大利语国家
            ["IT"] = "it-IT",    // 意大利
            ["SM"] = "it-SM",    // 圣马力诺
            ["VA"] = "it-VA",    // 梵蒂冈

            // 葡萄牙语国家
            ["PT"] = "pt-PT",    // 葡萄牙
            ["BR"] = "pt-BR",    // 巴西

            // 荷兰语国家
            ["NL"] = "nl-NL",    // 荷兰

            // 北欧语言
            ["SE"] = "sv-SE",    // 瑞典
            ["NO"] = "nb-NO",    // 挪威
            ["DK"] = "da-DK",    // 丹麦
            ["FI"] = "fi-FI",    // 芬兰
            ["IS"] = "is-IS",    // 冰岛

            // 东欧语言
            ["PL"] = "pl-PL",    // 波兰
            ["CZ"] = "cs-CZ",    // 捷克
            ["SK"] = "sk-SK",    // 斯洛伐克
            ["HU"] = "hu-HU",    // 匈牙利
            ["RO"] = "ro-RO",    // 罗马尼亚
            ["BG"] = "bg-BG",    // 保加利亚
            ["HR"] = "hr-HR",    // 克罗地亚
            ["SI"] = "sl-SI",    // 斯洛文尼亚
            ["EE"] = "et-EE",    // 爱沙尼亚
            ["LV"] = "lv-LV",    // 拉脱维亚
            ["LT"] = "lt-LT",    // 立陶宛

            // 俄语国家
            ["RU"] = "ru-RU",    // 俄罗斯
            ["BY"] = "ru-BY",    // 白俄罗斯
            ["KZ"] = "ru-KZ",    // 哈萨克斯坦

            // 亚洲语言
            ["CN"] = "zh-CN",    // 中国（简体中文）
            ["TW"] = "zh-TW",    // 台湾（繁体中文）
            ["HK"] = "zh-HK",    // 香港（繁体中文）
            ["JP"] = "ja-JP",    // 日本
            ["KR"] = "ko-KR",    // 韩国
            ["TH"] = "th-TH",    // 泰国
            ["VN"] = "vi-VN",    // 越南
            ["ID"] = "id-ID",    // 印度尼西亚
            ["MY"] = "ms-MY",    // 马来西亚
            ["SG"] = "en-SG",    // 新加坡（英语）
            ["PH"] = "en-PH",    // 菲律宾（英语）
            ["IN"] = "hi-IN",    // 印度（印地语）

            // 阿拉伯语国家
            ["SA"] = "ar-SA",    // 沙特阿拉伯
            ["AE"] = "ar-AE",    // 阿联酋
            ["EG"] = "ar-EG",    // 埃及
            ["JO"] = "ar-JO",    // 约旦
            ["LB"] = "ar-LB",    // 黎巴嫩
            ["SY"] = "ar-SY",    // 叙利亚
            ["IQ"] = "ar-IQ",    // 伊拉克
            ["KW"] = "ar-KW",    // 科威特
            ["QA"] = "ar-QA",    // 卡塔尔
            ["BH"] = "ar-BH",    // 巴林
            ["OM"] = "ar-OM",    // 阿曼
            ["YE"] = "ar-YE",    // 也门
            ["MA"] = "ar-MA",    // 摩洛哥
            ["TN"] = "ar-TN",    // 突尼斯
            ["DZ"] = "ar-DZ",    // 阿尔及利亚
            ["LY"] = "ar-LY",    // 利比亚

            // 土耳其语
            ["TR"] = "tr-TR",    // 土耳其

            // 希腊语
            ["GR"] = "el-GR",    // 希腊

            // 希伯来语
            ["IL"] = "he-IL",    // 以色列

            // 波斯语
            ["IR"] = "fa-IR",    // 伊朗

            // 其他语言
            ["UA"] = "uk-UA",    // 乌克兰
            ["RS"] = "sr-RS",    // 塞尔维亚
            ["BA"] = "bs-BA",    // 波斯尼亚
            ["MK"] = "mk-MK",    // 北马其顿
            ["AL"] = "sq-AL",    // 阿尔巴尼亚
            ["MT"] = "mt-MT",    // 马耳他
            ["CY"] = "el-CY",    // 塞浦路斯（希腊语）
        };

        /// <summary>
        /// 根据国家代码获取对应的语言代码
        /// </summary>
        /// <param name="countryCode">国家代码（如US、CN、JP等）</param>
        /// <returns>语言代码，如果找不到则返回"en-US"作为默认值</returns>
        public static string GetLanguageCode(string countryCode)
        {
            if (string.IsNullOrEmpty(countryCode))
                return "en-US";

            var code = countryCode.ToUpper().Trim();
            return CountryToLanguageMapping.TryGetValue(code, out var languageCode) ? languageCode : "en-US";
        }

        /// <summary>
        /// 根据国家代码获取Chrome浏览器的语言参数
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <returns>Chrome语言参数</returns>
        public static string GetChromeLanguageArg(string countryCode)
        {
            var languageCode = GetLanguageCode(countryCode);
            return $"--lang={languageCode}";
        }

        /// <summary>
        /// 根据国家代码获取Accept-Language头部值
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <returns>Accept-Language头部值</returns>
        public static string GetAcceptLanguageHeader(string countryCode)
        {
            var languageCode = GetLanguageCode(countryCode);
            
            // 构建Accept-Language头部，包含主语言和备选语言
            var primaryLang = languageCode.Split('-')[0];
            if (primaryLang == "en")
            {
                return $"{languageCode},en;q=0.9";
            }
            else
            {
                return $"{languageCode},{primaryLang};q=0.9,en;q=0.8";
            }
        }

        /// <summary>
        /// 检查国家代码是否有对应的语言映射
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <returns>如果有映射返回true，否则返回false</returns>
        public static bool HasLanguageMapping(string countryCode)
        {
            if (string.IsNullOrEmpty(countryCode))
                return false;

            return CountryToLanguageMapping.ContainsKey(countryCode.ToUpper().Trim());
        }

        /// <summary>
        /// 获取所有支持的国家代码和对应的语言代码
        /// </summary>
        /// <returns>国家代码和语言代码的字典</returns>
        public static Dictionary<string, string> GetAllLanguageMappings()
        {
            return new Dictionary<string, string>(CountryToLanguageMapping);
        }

        /// <summary>
        /// 根据国家代码获取语言的显示名称
        /// </summary>
        /// <param name="countryCode">国家代码</param>
        /// <returns>语言显示名称</returns>
        public static string GetLanguageDisplayName(string countryCode)
        {
            var languageCode = GetLanguageCode(countryCode);
            
            return languageCode switch
            {
                "en-US" => "English (United States)",
                "en-GB" => "English (United Kingdom)",
                "zh-CN" => "中文 (简体)",
                "zh-TW" => "中文 (繁體)",
                "ja-JP" => "日本語",
                "ko-KR" => "한국어",
                "es-ES" => "Español (España)",
                "es-MX" => "Español (México)",
                "fr-FR" => "Français",
                "de-DE" => "Deutsch",
                "it-IT" => "Italiano",
                "pt-BR" => "Português (Brasil)",
                "pt-PT" => "Português (Portugal)",
                "ru-RU" => "Русский",
                "ar-SA" => "العربية",
                "th-TH" => "ไทย",
                "tr-TR" => "Türkçe",
                "hi-IN" => "हिन्दी",
                "vi-VN" => "Tiếng Việt",
                "id-ID" => "Bahasa Indonesia",
                "ms-MY" => "Bahasa Melayu",
                _ => languageCode
            };
        }
    }
}
