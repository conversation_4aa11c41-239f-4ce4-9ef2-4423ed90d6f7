# 浏览器语言设置功能说明

## 功能概述

本功能实现了根据数据中的国家代码自动设置Chrome浏览器语言的能力。无论是单线程还是多线程模式，无论是本地Chrome还是无痕Chrome，都能根据当前处理的数据最后一个字段（国家编号）自动设定浏览器的语言环境。

## 功能特点

### 1. 全面的语言支持
- 支持150+个国家和地区的语言映射
- 包含主要语言：英语、中文、日语、韩语、西班牙语、法语、德语、俄语、阿拉伯语等
- 自动处理地区变体（如en-US、en-GB、es-ES、es-MX等）

### 2. 多层次语言设置
- **Chrome启动参数**：`--lang=语言代码`
- **浏览器上下文**：设置locale和Accept-Language头部
- **页面级别**：通过CDP设置语言环境（多线程模式）

### 3. 智能默认处理
- 未知国家代码自动使用英语（en-US）
- 空值或无效值的安全处理
- 向后兼容现有数据格式

## 数据格式

数据文件中的第15个字段为国家代码（两位字母代码），例如：
```
<EMAIL>|password|姓名|公司|地址|城市|州|邮编|卡号|月|年|CVV|持卡人|邮箱密码|US
```

最后的"US"就是国家代码，系统会根据此代码设置浏览器语言为英语（美国）。

## 支持的国家代码示例

| 国家代码 | 语言设置 | 显示名称 |
|---------|---------|---------|
| US | en-US | English (United States) |
| CN | zh-CN | 中文 (简体) |
| JP | ja-JP | 日本語 |
| KR | ko-KR | 한국어 |
| DE | de-DE | Deutsch |
| FR | fr-FR | Français |
| ES | es-ES | Español (España) |
| MX | es-MX | Español (México) |
| BR | pt-BR | Português (Brasil) |
| RU | ru-RU | Русский |
| TH | th-TH | ไทย |
| TR | tr-TR | Türkçe |
| SA | ar-SA | العربية |
| IN | hi-IN | हिन्दी |

## 工作原理

### 单线程模式
1. 加载数据时读取国家代码
2. 启动浏览器时添加语言参数
3. 创建浏览器上下文时设置locale和Accept-Language

### 多线程模式
1. 浏览器启动时使用默认语言
2. 分配数据后重新创建浏览器上下文
3. 通过CDP在页面级别设置语言环境

## 日志记录

系统会在日志中记录语言设置过程：
```
浏览器语言设置: 国家代码=CN, 语言=中文 (简体), 参数=--lang=zh-CN
浏览器上下文语言设置: 国家代码=CN, locale=zh-CN, Accept-Language=zh-CN,zh;q=0.9,en;q=0.8
```

## 技术实现

### 核心服务类
- `CountryLanguageService`：国家代码到语言映射服务
- `AutomationService`：浏览器自动化服务（已增强语言设置功能）

### 关键方法
- `GetLanguageCode(countryCode)`：获取语言代码
- `GetChromeLanguageArg(countryCode)`：获取Chrome语言参数
- `GetAcceptLanguageHeader(countryCode)`：获取Accept-Language头部
- `SetPageLanguageByCountryCode(data)`：设置页面语言（多线程模式）

## 使用说明

1. **数据准备**：确保数据文件中包含国家代码字段
2. **正常使用**：无需额外配置，系统自动根据国家代码设置语言
3. **日志查看**：可在日志中查看语言设置详情
4. **故障排除**：如果语言设置失败，会在日志中记录错误信息

## 兼容性

- ✅ 单线程模式 + 本地Chrome
- ✅ 单线程模式 + 无痕Chrome  
- ✅ 多线程模式 + 本地Chrome
- ✅ 多线程模式 + 无痕Chrome
- ✅ 向后兼容无国家代码的旧数据

## 注意事项

1. **数据格式**：确保国家代码为标准的两位字母代码（如US、CN、JP）
2. **大小写**：系统会自动转换为大写，支持小写输入
3. **默认行为**：未知国家代码会使用英语，不会导致程序错误
4. **性能影响**：语言设置过程很快，对整体性能影响微乎其微

## 更新历史

- **v1.0**：初始实现，支持150+国家语言映射
- 支持多层次语言设置（启动参数、上下文、页面级别）
- 完整的单线程和多线程模式支持
