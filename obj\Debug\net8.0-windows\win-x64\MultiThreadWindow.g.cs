﻿#pragma checksum "..\..\..\..\MultiThreadWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C25FA9ACBDC5FB402111A97A414E26B193919C76"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using AWSAutoRegister;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AWSAutoRegister {
    
    
    /// <summary>
    /// MultiThreadWindow
    /// </summary>
    public partial class MultiThreadWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 120 "..\..\..\..\MultiThreadWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PauseAllBtn;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\MultiThreadWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResumeAllBtn;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\MultiThreadWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TerminateAllBtn;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\MultiThreadWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BackToMainBtn;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\MultiThreadWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl ThreadListView;
        
        #line default
        #line hidden
        
        
        #line 274 "..\..\..\..\MultiThreadWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\..\MultiThreadWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar OverallProgress;
        
        #line default
        #line hidden
        
        
        #line 291 "..\..\..\..\MultiThreadWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OverallProgressText;
        
        #line default
        #line hidden
        
        
        #line 303 "..\..\..\..\MultiThreadWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveThreadsText;
        
        #line default
        #line hidden
        
        
        #line 314 "..\..\..\..\MultiThreadWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalThreadsText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AWS;component/multithreadwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\MultiThreadWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PauseAllBtn = ((System.Windows.Controls.Button)(target));
            
            #line 125 "..\..\..\..\MultiThreadWindow.xaml"
            this.PauseAllBtn.Click += new System.Windows.RoutedEventHandler(this.PauseAllBtn_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ResumeAllBtn = ((System.Windows.Controls.Button)(target));
            
            #line 131 "..\..\..\..\MultiThreadWindow.xaml"
            this.ResumeAllBtn.Click += new System.Windows.RoutedEventHandler(this.ResumeAllBtn_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.TerminateAllBtn = ((System.Windows.Controls.Button)(target));
            
            #line 137 "..\..\..\..\MultiThreadWindow.xaml"
            this.TerminateAllBtn.Click += new System.Windows.RoutedEventHandler(this.TerminateAllBtn_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BackToMainBtn = ((System.Windows.Controls.Button)(target));
            
            #line 142 "..\..\..\..\MultiThreadWindow.xaml"
            this.BackToMainBtn.Click += new System.Windows.RoutedEventHandler(this.BackToMainBtn_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ThreadListView = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 9:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.OverallProgress = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 11:
            this.OverallProgressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.ActiveThreadsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TotalThreadsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 6:
            
            #line 235 "..\..\..\..\MultiThreadWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PauseThreadBtn_Click);
            
            #line default
            #line hidden
            break;
            case 7:
            
            #line 245 "..\..\..\..\MultiThreadWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ContinueThreadBtn_Click);
            
            #line default
            #line hidden
            break;
            case 8:
            
            #line 254 "..\..\..\..\MultiThreadWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TerminateThreadBtn_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

