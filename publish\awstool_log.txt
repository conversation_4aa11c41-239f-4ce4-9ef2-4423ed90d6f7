2025-07-31 12:25:12 [信息] AWS自动注册工具启动
2025-07-31 12:25:12 [信息] 程序版本: 1.0.0.0
2025-07-31 12:25:12 [信息] 启动时间: 2025-07-31 12:25:12
2025-07-31 12:25:12 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-07-31 12:25:12 [信息] 线程数量已选择: 1
2025-07-31 12:25:12 [信息] 线程数量选择初始化完成
2025-07-31 12:25:12 [信息] 程序初始化完成
2025-07-31 14:10:07 [信息] AWS自动注册工具启动
2025-07-31 14:10:07 [信息] 程序版本: 1.0.0.0
2025-07-31 14:10:07 [信息] 启动时间: 2025-07-31 14:10:07
2025-07-31 14:10:07 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-07-31 14:10:07 [信息] 线程数量已选择: 1
2025-07-31 14:10:07 [信息] 线程数量选择初始化完成
2025-07-31 14:10:07 [信息] 程序初始化完成
2025-07-31 14:10:10 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-07-31 14:10:13 [信息] 已选择文件: C:\Users\<USER>\Desktop\ID.txt
2025-07-31 14:10:14 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\ID.txt
2025-07-31 14:10:14 [信息] 成功加载 0 条数据
2025-07-31 14:10:15 [信息] 线程数量已选择: 2
2025-07-31 14:10:36 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-07-31 14:10:39 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-07-31-印度尼西亚.txt
2025-07-31 14:10:40 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-07-31-印度尼西亚.txt
2025-07-31 14:10:40 [信息] 成功加载 4 条数据
2025-07-31 14:11:29 [按钮操作] 开始注册 -> 启动注册流程
2025-07-31 14:11:29 [信息] 开始启动多线程注册，线程数量: 2
2025-07-31 14:11:29 [信息] 开始启动多线程注册，线程数量: 2，数据条数: 4
2025-07-31 14:11:29 [信息] 所有线程已停止并清理
2025-07-31 14:11:29 [信息] 正在初始化多线程服务...
2025-07-31 14:11:29 [信息] 手机号码管理器已初始化，将在第一个线程完成第二页后获取手机号码
2025-07-31 14:11:29 [信息] 多线程服务初始化完成
2025-07-31 14:11:29 [信息] 数据分配完成：共4条数据分配给2个线程
2025-07-31 14:11:29 [信息] 线程1分配到2条数据
2025-07-31 14:11:29 [信息] 线程2分配到2条数据
2025-07-31 14:11:29 [信息] 屏幕工作区域: 1280x672
2025-07-31 14:11:29 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x310), 列1行1, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-07-31 14:11:29 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-07-31 14:11:29 线程1：[信息] 添加数据到队列: <EMAIL>
2025-07-31 14:11:29 线程1：[信息] 添加数据到队列: <EMAIL>
2025-07-31 14:11:29 [信息] 线程1已创建，窗口位置: (0, 0)
2025-07-31 14:11:29 [信息] 屏幕工作区域: 1280x672
2025-07-31 14:11:29 [信息] 线程2窗口布局: 位置(0, 329), 大小(384x310), 列1行2, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-07-31 14:11:29 线程2：[信息] 已创建，窗口位置: (0, 329)
2025-07-31 14:11:29 线程2：[信息] 添加数据到队列: <EMAIL>
2025-07-31 14:11:29 线程2：[信息] 添加数据到队列: <EMAIL>
2025-07-31 14:11:29 [信息] 线程2已创建，窗口位置: (0, 329)
2025-07-31 14:11:29 [信息] 多线程注册启动成功，共2个线程
2025-07-31 14:11:29 线程1：[信息] 开始启动注册流程
2025-07-31 14:11:29 线程2：[信息] 开始启动注册流程
2025-07-31 14:11:29 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-31 14:11:29 线程2：[信息] 开始启动浏览器: 位置(0, 329), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-31 14:11:29 线程2：[信息] 启动无痕Chrome浏览器...
2025-07-31 14:11:29 线程1：[信息] 启动无痕Chrome浏览器...
2025-07-31 14:11:29 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-07-31 14:11:29 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-07-31 14:11:29 [信息] 多线程管理窗口已初始化
2025-07-31 14:11:29 [信息] UniformGrid列数已更新为: 1
2025-07-31 14:11:29 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-07-31 14:11:29 [信息] 多线程管理窗口已打开
2025-07-31 14:11:29 [信息] 多线程注册启动成功，共2个线程
2025-07-31 14:11:31 [信息] UniformGrid列数已更新为: 1
2025-07-31 14:11:31 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-07-31 14:11:31 线程1：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-07-31 14:11:31 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-07-31 14:11:31 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-31 14:11:31 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-07-31 14:11:31 [信息] UniformGrid列数已更新为: 1
2025-07-31 14:11:31 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-07-31 14:11:31 线程2：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-07-31 14:11:31 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-07-31 14:11:31 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0
2025-07-31 14:11:31 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-07-31 14:11:32 线程2：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-07-31 14:11:32 线程1：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-07-31 14:11:36 线程2：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-07-31 14:11:36 线程1：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-07-31 14:11:37 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 0%)
2025-07-31 14:11:37 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-07-31 14:11:37 线程2：[信息] 浏览器启动成功
2025-07-31 14:11:37 线程2：[信息] 获取下一个数据: <EMAIL>
2025-07-31 14:11:37 线程2：[信息] 开始处理账户: <EMAIL>
2025-07-31 14:11:37 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-07-31 14:11:37 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-07-31 14:11:37 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-07-31 14:11:37 线程2：[信息] [信息] 多线程模式：为支持语言设置，创建新的浏览器上下文... (进度: 98%)
2025-07-31 14:11:37 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 0%)
2025-07-31 14:11:37 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-07-31 14:11:37 线程1：[信息] 浏览器启动成功
2025-07-31 14:11:37 线程1：[信息] 获取下一个数据: <EMAIL>
2025-07-31 14:11:37 线程1：[信息] 开始处理账户: <EMAIL>
2025-07-31 14:11:37 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-07-31 14:11:37 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-07-31 14:11:37 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-07-31 14:11:37 线程1：[信息] [信息] 多线程模式：为支持语言设置，创建新的浏览器上下文... (进度: 98%)
2025-07-31 14:11:37 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-07-31 14:11:37 [信息] 随机选择时区: America/Los_Angeles
2025-07-31 14:11:37 线程2：[信息] [信息] 设置浏览器上下文语言: Bahasa Indonesia (locale: id-ID) (进度: 98%)
2025-07-31 14:11:37 [信息] 浏览器上下文语言设置: 国家代码=ID, locale=id-ID, Accept-Language=id-ID,id;q=0.9,en;q=0.8
2025-07-31 14:11:37 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-31 14:11:37 [信息] 随机选择时区: America/Los_Angeles
2025-07-31 14:11:37 线程1：[信息] [信息] 设置浏览器上下文语言: Bahasa Indonesia (locale: id-ID) (进度: 98%)
2025-07-31 14:11:37 [信息] 浏览器上下文语言设置: 国家代码=ID, locale=id-ID, Accept-Language=id-ID,id;q=0.9,en;q=0.8
2025-07-31 14:11:37 线程2：[信息] [信息] 创建新的浏览器上下文 - UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-07-31 14:11:37 线程2：[信息] [信息] 设置随机时区: America/Los_Angeles (进度: 98%)
2025-07-31 14:11:37 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-07-31 14:11:37 线程1：[信息] [信息] 创建新的浏览器上下文 - UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-07-31 14:11:37 线程1：[信息] [信息] 设置随机时区: America/Los_Angeles (进度: 98%)
2025-07-31 14:11:37 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-07-31 14:11:39 线程2：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-07-31 14:11:39 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-07-31 14:11:39 线程1：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-07-31 14:11:39 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-07-31 14:11:39 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-07-31 14:11:39 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-07-31 14:11:39 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-07-31 14:11:39 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-07-31 14:11:39 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-07-31 14:11:39 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-07-31 14:11:50 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-07-31 14:11:50 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-07-31 14:11:50 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-07-31 14:11:50 [信息] 第一页加载完成，找到验证邮箱按钮
2025-07-31 14:11:50 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-07-31 14:11:52 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-07-31 14:11:52 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-07-31 14:11:52 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-07-31 14:11:52 [信息] 第一页加载完成，找到验证邮箱按钮
2025-07-31 14:11:53 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-07-31 14:11:53 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-07-31 14:11:53 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-07-31 14:11:53 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-07-31 14:11:53 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-07-31 14:11:53 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-07-31 14:11:53 线程1：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-07-31 14:11:53 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-07-31 14:11:53 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-07-31 14:11:53 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-07-31 14:11:54 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-07-31 14:11:56 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-07-31 14:11:56 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-07-31 14:11:56 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-07-31 14:11:56 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-07-31 14:11:57 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-07-31 14:11:57 线程2：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-07-31 14:11:57 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-07-31 14:11:57 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-07-31 14:11:57 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-07-31 14:11:57 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-07-31 14:11:58 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 34542 字节 (进度: 100%)
2025-07-31 14:11:58 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，34542字节，复杂度符合要求 (进度: 100%)
2025-07-31 14:11:58 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-07-31 14:12:00 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"ngn533"},"taskId":"44740d78-6dd5-11f0-8591-e28f2cb08a64"} (进度: 100%)
2025-07-31 14:12:00 线程1：[信息] [信息] 第一页第1次识别结果: ngn533 → 转换为小写: ngn533 (进度: 100%)
2025-07-31 14:12:00 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-07-31 14:12:00 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35469 字节 (进度: 100%)
2025-07-31 14:12:00 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，35469字节，复杂度符合要求 (进度: 100%)
2025-07-31 14:12:00 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-07-31 14:12:00 线程1：[信息] [信息] 已填入验证码: ngn533 (进度: 100%)
2025-07-31 14:12:00 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-07-31 14:12:01 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"xnfsfx"},"taskId":"4533d482-6dd5-11f0-be4a-ae68cbd38ae9"} (进度: 100%)
2025-07-31 14:12:01 线程2：[信息] [信息] 第一页第1次识别结果: xnfsfx → 转换为小写: xnfsfx (进度: 100%)
2025-07-31 14:12:01 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-07-31 14:12:01 线程2：[信息] [信息] 已填入验证码: xnfsfx (进度: 100%)
2025-07-31 14:12:02 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-07-31 14:12:02 线程1：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-07-31 14:12:02 线程1：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-07-31 14:12:02 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-07-31 14:12:02 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-07-31 14:12:02 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-07-31 14:12:02 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-07-31 14:12:02 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-07-31 14:12:02 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-07-31 14:12:02 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-07-31 14:12:02 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-07-31 14:12:02 [信息] [线程1] 等待2秒后开始第一次触发...
2025-07-31 14:12:04 线程2：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-07-31 14:12:04 线程2：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-07-31 14:12:04 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-07-31 14:12:04 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-07-31 14:12:04 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-07-31 14:12:04 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-07-31 14:12:04 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-07-31 14:12:04 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-07-31 14:12:04 [信息] [线程2] 等待2秒后开始第一次触发...
2025-07-31 14:12:04 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-07-31 14:12:04 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-07-31 14:12:04 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-07-31 14:12:04 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-31 14:12:04
2025-07-31 14:12:06 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-07-31 14:12:06 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-31 14:12:06
2025-07-31 14:12:07 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-07-31 14:12:07 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-31 14:12:07
2025-07-31 14:12:09 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-07-31 14:12:09 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-31 14:12:09
2025-07-31 14:12:10 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-07-31 14:12:10 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-31 14:12:10
2025-07-31 14:12:11 [信息] [线程1] 邮箱验证码获取成功: 863624，立即停止重复请求
2025-07-31 14:12:11 [信息] [线程1] 已清理请求文件，停止重复触发
2025-07-31 14:12:11 [信息] [线程1] 已清理响应文件
2025-07-31 14:12:11 线程1：[信息] [信息] 验证码获取成功: 863624，正在自动填入... (进度: 25%)
2025-07-31 14:12:11 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-07-31 14:12:11 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-07-31 14:12:11 [信息] [线程2] 邮箱验证码获取成功: 742933，立即停止重复请求
2025-07-31 14:12:11 [信息] [线程2] 已清理请求文件，停止重复触发
2025-07-31 14:12:11 [信息] [线程2] 已清理响应文件
2025-07-31 14:12:11 线程2：[信息] [信息] 验证码获取成功: 742933，正在自动填入... (进度: 25%)
2025-07-31 14:12:11 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-07-31 14:12:11 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-07-31 14:12:12 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-07-31 14:12:12 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-07-31 14:12:15 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-07-31 14:12:15 线程1：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-07-31 14:12:18 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-07-31 14:12:18 线程2：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-07-31 14:12:19 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-07-31 14:12:19 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-07-31 14:12:19 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-07-31 14:12:19 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-07-31 14:12:19 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-07-31 14:12:19 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-07-31 14:12:20 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-07-31 14:12:20 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-07-31 14:12:23 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-07-31 14:12:23 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-07-31 14:12:23 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-07-31 14:12:23 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-07-31 14:12:23 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-07-31 14:12:23 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-07-31 14:12:27 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-07-31 14:12:27 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-07-31 14:12:29 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-07-31 14:12:29 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-07-31 14:12:42 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-07-31 14:12:42 线程2：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 38%)
2025-07-31 14:12:43 线程2：[信息] [信息] 数据国家代码为ID，需要选择Indonesia (进度: 38%)
2025-07-31 14:12:43 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-07-31 14:12:43 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-07-31 14:12:43 线程1：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 38%)
2025-07-31 14:12:44 线程2：[信息] [信息] 后台获取手机号码成功: +527411511188，已保存到注册数据 (进度: 38%)
2025-07-31 14:12:46 线程1：[信息] [信息] 后台获取手机号码成功: +526646953843，已保存到注册数据 (进度: 38%)
2025-07-31 14:12:46 线程1：[信息] [信息] 数据国家代码为ID，需要选择Indonesia (进度: 38%)
2025-07-31 14:12:46 线程2：[信息] [信息] 已选择国家: Indonesia (进度: 38%)
2025-07-31 14:12:46 线程2：[信息] [信息] 已成功选择国家: Indonesia (进度: 38%)
2025-07-31 14:12:46 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-07-31 14:12:46 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-07-31 14:12:46 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-07-31 14:12:47 线程1：[信息] [信息] 已选择国家: Indonesia (进度: 38%)
2025-07-31 14:12:47 线程1：[信息] [信息] 已成功选择国家: Indonesia (进度: 38%)
2025-07-31 14:12:47 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-07-31 14:12:48 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-07-31 14:12:48 线程2：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-07-31 14:12:49 线程2：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-07-31 14:12:49 线程2：[信息] [信息] 已自动获取并填入手机号码: +527411511188 (进度: 38%)
2025-07-31 14:12:50 线程2：[信息] [信息] 使用已获取的手机号码: +527411511188（保存本地号码: 7411511188） (进度: 38%)
2025-07-31 14:12:50 线程1：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-07-31 14:12:50 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-07-31 14:12:51 线程1：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-07-31 14:12:51 线程1：[信息] [信息] 已自动获取并填入手机号码: +526646953843 (进度: 38%)
2025-07-31 14:12:52 线程1：[信息] [信息] 使用已获取的手机号码: +526646953843（保存本地号码: 6646953843） (进度: 38%)
2025-07-31 14:12:52 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-07-31 14:12:53 线程2：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-07-31 14:12:54 线程2：[信息] [信息] 正在选择月份: September (进度: 38%)
2025-07-31 14:12:54 线程2：[信息] [信息] 已选择月份（标准选项）: September (进度: 38%)
2025-07-31 14:12:55 线程2：[信息] [信息] 正在选择年份: 2028 (进度: 38%)
2025-07-31 14:12:55 线程1：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-07-31 14:12:55 线程2：[信息] [信息] 已选择年份（标准选项）: 2028 (进度: 38%)
2025-07-31 14:12:56 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-07-31 14:12:56 线程1：[信息] [信息] 正在选择月份: February (进度: 38%)
2025-07-31 14:12:56 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-07-31 14:12:56 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-07-31 14:12:56 线程1：[信息] [信息] 已选择月份（标准选项）: February (进度: 38%)
2025-07-31 14:12:57 线程1：[信息] [信息] 正在选择年份: 2029 (进度: 38%)
2025-07-31 14:12:57 线程1：[信息] [信息] 已选择年份（标准选项）: 2029 (进度: 38%)
2025-07-31 14:12:57 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-07-31 14:12:57 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-07-31 14:12:57 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-07-31 14:13:01 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-07-31 14:13:02 线程2：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-07-31 14:13:02 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-07-31 14:13:02 线程2：[信息] [信息] 已清空并重新填写手机号码: 7411511188 (进度: 38%)
2025-07-31 14:13:02 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-07-31 14:13:03 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-07-31 14:13:03 线程1：[信息] [信息] 已清空并重新填写手机号码: 6646953843 (进度: 38%)
2025-07-31 14:13:03 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-07-31 14:13:04 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-07-31 14:13:04 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-07-31 14:13:04 线程2：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-07-31 14:13:04 线程2：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-07-31 14:13:04 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-07-31 14:13:05 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-07-31 14:13:05 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-07-31 14:13:05 线程1：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-07-31 14:13:05 线程1：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-07-31 14:13:05 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-07-31 14:13:07 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-07-31 14:13:07 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-07-31 14:13:08 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-07-31 14:13:08 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-07-31 14:13:10 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34566 字节 (进度: 100%)
2025-07-31 14:13:10 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，34566字节，复杂度符合要求 (进度: 100%)
2025-07-31 14:13:10 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-07-31 14:13:12 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35015 字节 (进度: 100%)
2025-07-31 14:13:12 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35015字节，复杂度符合要求 (进度: 100%)
2025-07-31 14:13:12 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-07-31 14:13:12 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"m8fdyg"},"taskId":"6f4b6078-6dd5-11f0-9ce8-e28f2cb08a64"} (进度: 100%)
2025-07-31 14:13:12 线程2：[信息] [信息] 第六页第1次识别结果: m8fdyg → 转换为小写: m8fdyg (进度: 100%)
2025-07-31 14:13:12 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-07-31 14:13:12 线程2：[信息] [信息] 第六页已填入验证码: m8fdyg (进度: 100%)
2025-07-31 14:13:12 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-07-31 14:13:14 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"bn3x8r"},"taskId":"702b4206-6dd5-11f0-8cf8-261ead5dd06d"} (进度: 100%)
2025-07-31 14:13:14 线程1：[信息] [信息] 第六页第1次识别结果: bn3x8r → 转换为小写: bn3x8r (进度: 100%)
2025-07-31 14:13:14 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-07-31 14:13:14 线程1：[信息] [信息] 第六页已填入验证码: bn3x8r (进度: 100%)
2025-07-31 14:13:14 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-07-31 14:13:15 线程2：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-07-31 14:13:15 线程2：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-07-31 14:13:18 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-07-31 14:13:18 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-07-31 14:13:19 线程2：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-07-31 14:13:21 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-07-31 14:13:22 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-07-31 14:13:22 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-07-31 14:13:22 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-07-31 14:13:22 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-07-31 14:13:24 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-07-31 14:13:24 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-07-31 14:13:24 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-07-31 14:13:24 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-07-31 14:13:27 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-07-31 14:13:27 线程2：[信息] [信息] 线程2开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-07-31 14:13:27 线程2：[信息] [信息] 线程2第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-07-31 14:13:29 线程2：[信息] [信息] 线程2第1次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-31 14:13:29 线程2：[信息] [信息] 线程2等待8秒后进行下一次尝试... (进度: 100%)
2025-07-31 14:13:29 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-07-31 14:13:30 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-07-31 14:13:30 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-07-31 14:13:30 线程1：[信息] [信息] 线程1第1次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-31 14:13:30 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-31 14:13:37 线程2：[信息] [信息] 线程2第2次尝试获取验证码...（剩余6次尝试） (进度: 100%)
2025-07-31 14:13:37 线程2：[信息] [信息] 线程2第2次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-31 14:13:37 线程2：[信息] [信息] 线程2等待8秒后进行下一次尝试... (进度: 100%)
2025-07-31 14:13:38 线程1：[信息] [信息] 线程1第2次尝试获取验证码...（剩余6次尝试） (进度: 100%)
2025-07-31 14:13:38 线程1：[信息] [信息] 线程1第2次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-31 14:13:38 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-31 14:13:45 线程2：[信息] [信息] 线程2第3次尝试获取验证码...（剩余5次尝试） (进度: 100%)
2025-07-31 14:13:45 线程2：[信息] [信息] 线程2第3次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-31 14:13:45 线程2：[信息] [信息] 线程2等待8秒后进行下一次尝试... (进度: 100%)
2025-07-31 14:13:46 线程1：[信息] [信息] 线程1第3次尝试获取验证码...（剩余5次尝试） (进度: 100%)
2025-07-31 14:13:46 线程1：[信息] [信息] 线程1第3次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-31 14:13:46 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-31 14:13:53 线程2：[信息] [信息] 线程2第4次尝试获取验证码...（剩余4次尝试） (进度: 100%)
2025-07-31 14:13:54 线程2：[信息] [信息] 线程2第4次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-31 14:13:54 线程2：[信息] [信息] 线程2等待8秒后进行下一次尝试... (进度: 100%)
2025-07-31 14:13:54 线程1：[信息] [信息] 线程1第4次尝试获取验证码...（剩余4次尝试） (进度: 100%)
2025-07-31 14:13:55 线程1：[信息] [信息] 线程1第4次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-31 14:13:55 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-31 14:14:02 线程2：[信息] [信息] 线程2第5次尝试获取验证码...（剩余3次尝试） (进度: 100%)
2025-07-31 14:14:02 线程2：[信息] [信息] 线程2第5次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-31 14:14:02 线程2：[信息] [信息] 线程2等待8秒后进行下一次尝试... (进度: 100%)
2025-07-31 14:14:03 线程1：[信息] [信息] 线程1第5次尝试获取验证码...（剩余3次尝试） (进度: 100%)
2025-07-31 14:14:03 线程1：[信息] [信息] 线程1第5次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-31 14:14:03 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-31 14:14:10 线程2：[信息] [信息] 线程2第6次尝试获取验证码...（剩余2次尝试） (进度: 100%)
2025-07-31 14:14:10 线程2：[信息] [信息] 线程2第6次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-31 14:14:10 线程2：[信息] [信息] 线程2等待8秒后进行下一次尝试... (进度: 100%)
2025-07-31 14:14:11 线程1：[信息] [信息] 线程1第6次尝试获取验证码...（剩余2次尝试） (进度: 100%)
2025-07-31 14:14:11 线程1：[信息] [信息] 线程1第6次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-31 14:14:11 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-31 14:14:18 线程2：[信息] [信息] 线程2第7次尝试获取验证码...（剩余1次尝试） (进度: 100%)
2025-07-31 14:14:18 线程2：[信息] [信息] 线程2第7次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-31 14:14:18 线程2：[信息] [信息] 线程2等待8秒后进行下一次尝试... (进度: 100%)
2025-07-31 14:14:19 线程1：[信息] [信息] 线程1第7次尝试获取验证码...（剩余1次尝试） (进度: 100%)
2025-07-31 14:14:20 线程1：[信息] [信息] 线程1第7次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-31 14:14:20 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-31 14:14:27 线程2：[信息] [信息] 线程2第8次尝试获取验证码...（剩余0次尝试） (进度: 100%)
2025-07-31 14:14:27 线程2：[信息] [信息] 线程2第8次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-31 14:14:27 线程2：[信息] [信息] 线程2验证码获取失败，已尝试8次 (进度: 100%)
2025-07-31 14:14:27 [信息] 线程2手机号码已加入释放队列: +527411511188 (原因: 验证码获取失败)
2025-07-31 14:14:27 线程2：[信息] [信息] 线程2验证码获取失败: 获取失败，已尝试8次 (进度: 100%)
2025-07-31 14:14:27 线程2：[信息] [信息] 验证码获取失败，第1次重试... (进度: 100%)
2025-07-31 14:14:27 线程2：[信息] [信息] 正在自动重试第1次，同时加入黑名单和获取新号码... (进度: 100%)
2025-07-31 14:14:27 线程2：[信息] [信息] 正在返回上一页... (进度: 100%)
2025-07-31 14:14:27 线程2：[信息] [信息] 已点击返回按钮 (进度: 100%)
2025-07-31 14:14:28 线程1：[信息] [信息] 线程1第8次尝试获取验证码...（剩余0次尝试） (进度: 100%)
2025-07-31 14:14:28 线程2：[信息] [信息] 超时号码已加入黑名单 (进度: 100%)
2025-07-31 14:14:28 线程1：[信息] [信息] 线程1第8次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-31 14:14:28 线程1：[信息] [信息] 线程1验证码获取失败，已尝试8次 (进度: 100%)
2025-07-31 14:14:28 [信息] 线程1手机号码已加入释放队列: +526646953843 (原因: 验证码获取失败)
2025-07-31 14:14:28 线程1：[信息] [信息] 线程1验证码获取失败: 获取失败，已尝试8次 (进度: 100%)
2025-07-31 14:14:28 线程1：[信息] [信息] 验证码获取失败，第1次重试... (进度: 100%)
2025-07-31 14:14:28 线程1：[信息] [信息] 正在自动重试第1次，同时加入黑名单和获取新号码... (进度: 100%)
2025-07-31 14:14:28 线程1：[信息] [信息] 正在返回上一页... (进度: 100%)
2025-07-31 14:14:28 线程1：[信息] [信息] 已点击返回按钮 (进度: 100%)
2025-07-31 14:14:29 [信息] 定时检查发现2个待释放手机号码，开始批量释放
2025-07-31 14:14:29 [信息] 开始释放2个手机号码
2025-07-31 14:14:29 [信息] [手机API] 开始批量释放2个手机号码
2025-07-31 14:14:29 [信息] [手机API] 释放手机号码: +527411511188
2025-07-31 14:14:29 线程1：[信息] [信息] 超时号码已加入黑名单 (进度: 100%)
2025-07-31 14:14:29 [信息] [手机API] 手机号码释放成功: +527411511188
2025-07-31 14:14:30 线程2：[信息] [信息] 正在选择国家代码: mx (进度: 100%)
2025-07-31 14:14:30 [信息] [手机API] 释放手机号码: +526646953843
2025-07-31 14:14:30 线程2：[信息] [信息] 已打开国家代码下拉列表 (进度: 100%)
2025-07-31 14:14:30 [信息] [手机API] 手机号码释放成功: +526646953843
2025-07-31 14:14:31 [信息] [手机API] 批量释放完成: 成功2个, 失败0个
2025-07-31 14:14:31 [信息] 定时批量释放完成: 批量释放完成: 成功2个, 失败0个
2025-07-31 14:14:31 线程2：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-07-31 14:14:31 线程2：[信息] [信息] 后台获取新手机号码... (进度: 100%)
2025-07-31 14:14:31 线程1：[信息] [信息] 正在选择国家代码: mx (进度: 100%)
2025-07-31 14:14:31 线程1：[信息] [信息] 已打开国家代码下拉列表 (进度: 100%)
2025-07-31 14:14:32 线程2：[信息] [信息] 后台获取新手机号码成功: +529223659789，已保存到注册数据 (进度: 100%)
2025-07-31 14:14:32 线程2：[信息] [信息] 正在清空并填入新的手机号码... (进度: 100%)
2025-07-31 14:14:32 线程2：[信息] [信息] 已填入新手机号码: +529223659789 (进度: 100%)
2025-07-31 14:14:32 线程2：[信息] [信息] 正在点击发送短信按钮... (进度: 100%)
2025-07-31 14:14:33 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-07-31 14:14:33 线程1：[信息] [信息] 后台获取新手机号码... (进度: 100%)
2025-07-31 14:14:33 线程1：[信息] [信息] 后台获取新手机号码成功: +524443091385，已保存到注册数据 (进度: 100%)
2025-07-31 14:14:33 线程1：[信息] [信息] 正在清空并填入新的手机号码... (进度: 100%)
2025-07-31 14:14:33 线程1：[信息] [信息] 已填入新手机号码: +524443091385 (进度: 100%)
2025-07-31 14:14:33 线程1：[信息] [信息] 正在点击发送短信按钮... (进度: 100%)
2025-07-31 14:14:34 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-07-31 14:14:34 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-07-31 14:14:35 线程2：[信息] [信息] 新手机号码已填入，自动模式：开始处理图形验证码... (进度: 100%)
2025-07-31 14:14:35 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-07-31 14:14:35 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-07-31 14:14:35 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-07-31 14:14:36 线程1：[信息] [信息] 新手机号码已填入，自动模式：开始处理图形验证码... (进度: 100%)
2025-07-31 14:14:36 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-07-31 14:14:38 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-07-31 14:14:38 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-07-31 14:14:39 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-07-31 14:14:39 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-07-31 14:14:41 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34752 字节 (进度: 100%)
2025-07-31 14:14:41 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，34752字节，复杂度符合要求 (进度: 100%)
2025-07-31 14:14:41 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-07-31 14:14:43 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35062 字节 (进度: 100%)
2025-07-31 14:14:43 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35062字节，复杂度符合要求 (进度: 100%)
2025-07-31 14:14:43 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-07-31 14:14:43 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"b82dwy"},"taskId":"a586a896-6dd5-11f0-ac35-261ead5dd06d"} (进度: 100%)
2025-07-31 14:14:43 线程2：[信息] [信息] 第六页第1次识别结果: b82dwy → 转换为小写: b82dwy (进度: 100%)
2025-07-31 14:14:43 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-07-31 14:14:43 线程2：[信息] [信息] 第六页已填入验证码: b82dwy (进度: 100%)
2025-07-31 14:14:43 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-07-31 14:14:44 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"mw8c68"},"taskId":"a6782c5c-6dd5-11f0-8f95-ae68cbd38ae9"} (进度: 100%)
2025-07-31 14:14:44 线程1：[信息] [信息] 第六页第1次识别结果: mw8c68 → 转换为小写: mw8c68 (进度: 100%)
2025-07-31 14:14:44 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-07-31 14:14:44 线程1：[信息] [信息] 第六页已填入验证码: mw8c68 (进度: 100%)
2025-07-31 14:14:44 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-07-31 14:14:46 线程2：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-07-31 14:14:46 线程2：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-07-31 14:14:49 线程1：[信息] [信息] 第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-07-31 14:14:49 线程1：[信息] [信息] 第六页第1次识别异常: 验证码错误 (进度: 100%)
2025-07-31 14:14:49 线程2：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-07-31 14:14:51 线程1：[信息] [信息] 第六页第2次尝试自动识别图形验证码... (进度: 100%)
2025-07-31 14:14:52 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-07-31 14:14:52 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-07-31 14:14:52 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-07-31 14:14:52 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-07-31 14:14:54 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 31001 字节 (进度: 100%)
2025-07-31 14:14:54 线程1：[信息] [信息] ✅ 图片验证通过：200x71px，31001字节，复杂度符合要求 (进度: 100%)
2025-07-31 14:14:54 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-07-31 14:14:56 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"ybmpw6"},"taskId":"adc57ca8-6dd5-11f0-8f95-ae68cbd38ae9"} (进度: 100%)
2025-07-31 14:14:56 线程1：[信息] [信息] 第六页第2次识别结果: ybmpw6 → 转换为小写: ybmpw6 (进度: 100%)
2025-07-31 14:14:56 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-07-31 14:14:56 线程1：[信息] [信息] 第六页已填入验证码: ybmpw6 (进度: 100%)
2025-07-31 14:14:57 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-07-31 14:14:57 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-07-31 14:14:57 线程2：[信息] [信息] 线程2开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-07-31 14:14:57 线程2：[信息] [信息] 线程2第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-07-31 14:14:57 线程2：[信息] [信息] 线程2验证码获取成功: 6116 (进度: 100%)
2025-07-31 14:14:57 [信息] 线程2手机号码已加入释放队列: +529223659789 (原因: 获取验证码成功)
2025-07-31 14:14:57 线程2：[信息] [信息] 线程2验证码获取成功: 6116，立即填入验证码... (进度: 100%)
2025-07-31 14:14:58 线程2：[信息] [信息] 线程2已自动填入手机验证码: 6116 (进度: 100%)
2025-07-31 14:14:59 线程2：[信息] [信息] 线程2正在自动点击Continue按钮... (进度: 100%)
2025-07-31 14:14:59 线程2：[信息] [信息] 线程2手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-07-31 14:14:59 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-07-31 14:14:59 [信息] 开始释放1个手机号码
2025-07-31 14:14:59 [信息] [手机API] 开始批量释放1个手机号码
2025-07-31 14:14:59 [信息] [手机API] 释放手机号码: +529223659789
2025-07-31 14:14:59 [信息] [手机API] 手机号码释放成功: +529223659789
2025-07-31 14:15:00 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-07-31 14:15:00 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-07-31 14:15:01 线程1：[信息] [信息] 第2次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-07-31 14:15:01 线程1：[信息] [信息] 第六页第2次识别异常: 验证码错误 (进度: 100%)
2025-07-31 14:15:02 线程2：[信息] [信息] 检测到无资格错误提示: You are not eligible for new customer credits (进度: 100%)
2025-07-31 14:15:02 线程2：[信息] [信息] 线程2检测到卡号已被关联错误，注册失败 (进度: 100%)
2025-07-31 14:15:02 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：2T9ukQ3f76X ③AWS密码：8rNfdSeF ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联 (进度: 100%)
2025-07-31 14:15:02 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：2T9ukQ3f76X ③AWS密码：8rNfdSeF ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-07-31 14:15:02 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：2T9ukQ3f76X ③AWS密码：8rNfdSeF ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-07-31 14:15:02 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：2T9ukQ3f76X ③AWS密码：8rNfdSeF ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-07-31 14:15:02 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：2T9ukQ3f76X ③AWS密码：8rNfdSeF ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-07-31 14:15:02 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：2T9ukQ3f76X ③AWS密码：8rNfdSeF ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-07-31 14:15:02 线程2：[信息] 收到失败数据保存请求: 卡号已被关联, 数据: <EMAIL>
2025-07-31 14:15:02 [信息] 线程2请求保存失败数据: 卡号已被关联, 数据: <EMAIL>
2025-07-31 14:15:02 [信息] 已处理失败数据: <EMAIL>, 失败原因: 卡号已被关联
2025-07-31 14:15:02 [信息] 线程2失败数据已保存: 卡号已被关联, 数据: <EMAIL>
2025-07-31 14:15:02 线程2：[信息] [信息] 已通知保存失败数据，失败原因: 卡号已被关联 (进度: 0%)
2025-07-31 14:15:02 线程2：[信息] [信息] 线程2注册失败，数据已归类，注册流程终止 (进度: 0%)
2025-07-31 14:15:03 线程1：[信息] [信息] 第六页第3次尝试自动识别图形验证码... (进度: 100%)
2025-07-31 14:15:06 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 31419 字节 (进度: 100%)
2025-07-31 14:15:06 线程1：[信息] [信息] ✅ 图片验证通过：200x71px，31419字节，复杂度符合要求 (进度: 100%)
2025-07-31 14:15:06 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-07-31 14:15:08 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"ynzf"},"taskId":"b41bd160-6dd5-11f0-bdf6-0667277c6127"} (进度: 100%)
2025-07-31 14:15:08 线程1：[信息] [信息] 第六页第3次识别结果: ynzf → 转换为小写: ynzf (进度: 100%)
2025-07-31 14:15:08 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-07-31 14:15:08 线程1：[信息] [信息] 第六页已填入验证码: ynzf (进度: 100%)
2025-07-31 14:15:08 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-07-31 14:15:13 线程1：[信息] [信息] 第3次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-07-31 14:15:13 线程1：[信息] [信息] 第六页第3次识别异常: 验证码错误 (进度: 100%)
2025-07-31 14:15:13 线程1：[信息] [信息] 第六页图形验证码自动处理失败，转为手动模式 (进度: 100%)
2025-07-31 14:15:31 线程1：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 7 (进度: 100%)
2025-07-31 14:15:31 线程1：[信息] [信息] 第七页手动验证码已输入，检测当前页面状态... (进度: 100%)
2025-07-31 14:15:31 线程1：[信息] [信息] 第七页：智能检测当前页面状态... (进度: 100%)
2025-07-31 14:15:31 线程1：[信息] [信息] 🔧 [DEBUG] 使用修改后的HandleStep7SmartContinue方法 - 版本2025-07-28 (进度: 100%)
2025-07-31 14:15:32 线程1：[信息] [信息] 检测到验证码已填写: 7017位 (进度: 100%)
2025-07-31 14:15:32 线程1：[信息] [信息] 第七页：检测到用户已输入验证码，直接执行手动模式处理流程... (进度: 100%)
2025-07-31 14:15:32 线程1：[信息] [信息] 检测到验证码已填写: 7017位 (进度: 100%)
2025-07-31 14:15:32 线程1：[信息] [信息] 第七页：检测到验证码已填写，查找Continue按钮... (进度: 100%)
2025-07-31 14:15:32 线程1：[信息] [信息] 第七页：检测到Continue (step 4 of 5)按钮，自动点击... (进度: 100%)
2025-07-31 14:15:32 线程1：[信息] [信息] 第七页：已点击Continue (step 4 of 5)按钮，等待页面跳转... (进度: 100%)
2025-07-31 14:15:35 线程1：[信息] [信息] 第七页Continue按钮处理完成，开始执行后续注册流程... (进度: 100%)
2025-07-31 14:15:35 线程1：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-07-31 14:15:36 线程1：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-07-31 14:15:37 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-07-31 14:15:37 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-07-31 14:15:37 线程1：[信息] 已暂停
2025-07-31 14:15:37 [信息] 线程1已暂停
2025-07-31 14:15:37 [信息] 线程1已暂停
2025-07-31 14:16:05 线程1：[信息] [信息] 点击完成注册按钮失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Complete sign up" }) (进度: 100%)
2025-07-31 14:16:05 线程1：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-07-31 14:16:08 线程1：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-07-31 14:16:08 线程1：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-07-31 14:16:13 线程1：[信息] [信息] ❌ 控制台按钮处理失败: Timeout 5000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" }) to be visible (进度: 100%)
2025-07-31 14:16:13 [信息] 控制台按钮处理失败: Timeout 5000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" }) to be visible
2025-07-31 14:16:13 线程1：[信息] [信息] ⚠️ 密钥提取失败，按原本逻辑完成注册 (进度: 100%)
2025-07-31 14:16:13 [信息] 密钥提取失败，按原本逻辑完成注册
2025-07-31 14:16:13 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：6Af2sATMApC3 ③AWS密码：Ox9Zf4lJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 100%)
2025-07-31 14:16:13 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：6Af2sATMApC3 ③AWS密码：Ox9Zf4lJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-07-31 14:16:13 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：6Af2sATMApC3 ③AWS密码：Ox9Zf4lJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-07-31 14:16:13 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：6Af2sATMApC3 ③AWS密码：Ox9Zf4lJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-07-31 14:16:13 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：6Af2sATMApC3 ③AWS密码：Ox9Zf4lJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-07-31 14:16:13 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：6Af2sATMApC3 ③AWS密码：Ox9Zf4lJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-07-31 14:16:13 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-07-31 14:16:13 线程1：[信息] 账户注册完成(无密钥)，等待后续操作: <EMAIL>
2025-07-31 14:16:14 线程1：[信息] [信息] ✅ 注册成功（密钥提取失败） (进度: 98%)
2025-07-31 14:16:14 [信息] 注册完成（密钥提取失败）
2025-07-31 14:16:14 线程1：[信息] 已继续
2025-07-31 14:16:14 [信息] 开始处理数据完成事件: <EMAIL>
2025-07-31 14:16:14 [信息] 已将完成数据移动到注册成功区域: <EMAIL>
2025-07-31 14:16:14 [信息] 已完成数据移除: <EMAIL>
2025-07-31 14:16:14 [信息] 检测到多线程处理中数据为0，已重置按钮状态
2025-07-31 14:16:14 [信息] 数据完成事件处理完毕: <EMAIL>
2025-07-31 14:16:14 [信息] 线程1已继续
2025-07-31 14:16:14 线程1：[信息] 最终完成: 注册完成，无法提取密钥: <EMAIL> (类型: WithoutKeys)
2025-07-31 14:16:14 [信息] 线程1数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-07-31 14:27:35 [信息] 多线程窗口引用已清理
2025-07-31 14:27:35 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-07-31 14:27:35 [信息] 多线程管理窗口正在关闭
