﻿#pragma checksum "..\..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "87588F4A545C27618562A4C65C7C1846C2CAA9C2"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AWSAutoRegister {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 331 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SelectFileButton;
        
        #line default
        #line hidden
        
        
        #line 346 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FilePathTextBox;
        
        #line default
        #line hidden
        
        
        #line 356 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LoadDataButton;
        
        #line default
        #line hidden
        
        
        #line 395 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton AdsPowerModeRadio;
        
        #line default
        #line hidden
        
        
        #line 400 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton LocalChromeNormalModeRadio;
        
        #line default
        #line hidden
        
        
        #line 405 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton LocalChromeIncognitoModeRadio;
        
        #line default
        #line hidden
        
        
        #line 430 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ThreadCountComboBox;
        
        #line default
        #line hidden
        
        
        #line 445 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ThreadModeHint;
        
        #line default
        #line hidden
        
        
        #line 453 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenMultiThreadWindowBtn;
        
        #line default
        #line hidden
        
        
        #line 464 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid AdsPowerConfigGrid;
        
        #line default
        #line hidden
        
        
        #line 486 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ProfileIdTextBox;
        
        #line default
        #line hidden
        
        
        #line 495 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestConnectionButton;
        
        #line default
        #line hidden
        
        
        #line 506 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LocalChromeConfigGrid;
        
        #line default
        #line hidden
        
        
        #line 519 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestLocalChromeButton;
        
        #line default
        #line hidden
        
        
        #line 570 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton PhoneAutoModeRadio;
        
        #line default
        #line hidden
        
        
        #line 575 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton PhoneManualModeRadio;
        
        #line default
        #line hidden
        
        
        #line 588 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse PhoneModeIndicator;
        
        #line default
        #line hidden
        
        
        #line 593 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PhoneModeText;
        
        #line default
        #line hidden
        
        
        #line 603 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid PhoneApiConfigGrid;
        
        #line default
        #line hidden
        
        
        #line 625 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PhoneApiNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 646 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PhoneApiKeyTextBox;
        
        #line default
        #line hidden
        
        
        #line 656 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid PhoneApiConfigGrid2;
        
        #line default
        #line hidden
        
        
        #line 678 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PhoneApiProjectIdTextBox;
        
        #line default
        #line hidden
        
        
        #line 696 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CountrySearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 711 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup SearchResultsPopup;
        
        #line default
        #line hidden
        
        
        #line 731 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox SearchResultsListBox;
        
        #line default
        #line hidden
        
        
        #line 769 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid PhoneApiConfigGrid3;
        
        #line default
        #line hidden
        
        
        #line 777 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestPhoneApiButton;
        
        #line default
        #line hidden
        
        
        #line 785 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SavePhoneApiButton;
        
        #line default
        #line hidden
        
        
        #line 828 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton EmailGoogleRadio;
        
        #line default
        #line hidden
        
        
        #line 833 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton EmailMicrosoftRadio;
        
        #line default
        #line hidden
        
        
        #line 838 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton EmailManualModeRadio;
        
        #line default
        #line hidden
        
        
        #line 858 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EmailModeDescriptionText;
        
        #line default
        #line hidden
        
        
        #line 903 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton CaptchaYesModeRadio;
        
        #line default
        #line hidden
        
        
        #line 910 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton CaptchaCloudModeRadio;
        
        #line default
        #line hidden
        
        
        #line 917 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton CaptchaManualModeRadio;
        
        #line default
        #line hidden
        
        
        #line 939 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CaptchaModeDescriptionText;
        
        #line default
        #line hidden
        
        
        #line 974 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartButton;
        
        #line default
        #line hidden
        
        
        #line 985 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ContinueButton;
        
        #line default
        #line hidden
        
        
        #line 996 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PauseButton;
        
        #line default
        #line hidden
        
        
        #line 1007 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StopButton;
        
        #line default
        #line hidden
        
        
        #line 1057 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1081 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border BrowserModeIndicator;
        
        #line default
        #line hidden
        
        
        #line 1091 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BrowserModeText;
        
        #line default
        #line hidden
        
        
        #line 1160 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UnprocessedCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1180 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PendingCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1200 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProcessingCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1220 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CompletedCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1240 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FailedCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 1258 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AWS;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SelectFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 335 "..\..\..\..\MainWindow.xaml"
            this.SelectFileButton.Click += new System.Windows.RoutedEventHandler(this.SelectFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.FilePathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.LoadDataButton = ((System.Windows.Controls.Button)(target));
            
            #line 359 "..\..\..\..\MainWindow.xaml"
            this.LoadDataButton.Click += new System.Windows.RoutedEventHandler(this.LoadDataButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.AdsPowerModeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 399 "..\..\..\..\MainWindow.xaml"
            this.AdsPowerModeRadio.Checked += new System.Windows.RoutedEventHandler(this.BrowserModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 5:
            this.LocalChromeNormalModeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 404 "..\..\..\..\MainWindow.xaml"
            this.LocalChromeNormalModeRadio.Checked += new System.Windows.RoutedEventHandler(this.BrowserModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 6:
            this.LocalChromeIncognitoModeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 408 "..\..\..\..\MainWindow.xaml"
            this.LocalChromeIncognitoModeRadio.Checked += new System.Windows.RoutedEventHandler(this.BrowserModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ThreadCountComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 434 "..\..\..\..\MainWindow.xaml"
            this.ThreadCountComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ThreadCountComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ThreadModeHint = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.OpenMultiThreadWindowBtn = ((System.Windows.Controls.Button)(target));
            
            #line 460 "..\..\..\..\MainWindow.xaml"
            this.OpenMultiThreadWindowBtn.Click += new System.Windows.RoutedEventHandler(this.OpenMultiThreadWindowBtn_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.AdsPowerConfigGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 11:
            this.ProfileIdTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.TestConnectionButton = ((System.Windows.Controls.Button)(target));
            
            #line 497 "..\..\..\..\MainWindow.xaml"
            this.TestConnectionButton.Click += new System.Windows.RoutedEventHandler(this.TestConnectionButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.LocalChromeConfigGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 14:
            this.TestLocalChromeButton = ((System.Windows.Controls.Button)(target));
            
            #line 521 "..\..\..\..\MainWindow.xaml"
            this.TestLocalChromeButton.Click += new System.Windows.RoutedEventHandler(this.TestLocalChromeButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.PhoneAutoModeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 574 "..\..\..\..\MainWindow.xaml"
            this.PhoneAutoModeRadio.Checked += new System.Windows.RoutedEventHandler(this.PhoneModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 16:
            this.PhoneManualModeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 578 "..\..\..\..\MainWindow.xaml"
            this.PhoneManualModeRadio.Checked += new System.Windows.RoutedEventHandler(this.PhoneModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 17:
            this.PhoneModeIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 18:
            this.PhoneModeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.PhoneApiConfigGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 20:
            this.PhoneApiNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.PhoneApiKeyTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 22:
            this.PhoneApiConfigGrid2 = ((System.Windows.Controls.Grid)(target));
            return;
            case 23:
            this.PhoneApiProjectIdTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 24:
            this.CountrySearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 705 "..\..\..\..\MainWindow.xaml"
            this.CountrySearchTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.CountrySearchTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 706 "..\..\..\..\MainWindow.xaml"
            this.CountrySearchTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.CountrySearchTextBox_LostFocus);
            
            #line default
            #line hidden
            
            #line 707 "..\..\..\..\MainWindow.xaml"
            this.CountrySearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CountrySearchTextBox_TextChanged);
            
            #line default
            #line hidden
            
            #line 708 "..\..\..\..\MainWindow.xaml"
            this.CountrySearchTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.CountrySearchTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 25:
            this.SearchResultsPopup = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 26:
            this.SearchResultsListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 734 "..\..\..\..\MainWindow.xaml"
            this.SearchResultsListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SearchResultsListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 27:
            this.PhoneApiConfigGrid3 = ((System.Windows.Controls.Grid)(target));
            return;
            case 28:
            this.TestPhoneApiButton = ((System.Windows.Controls.Button)(target));
            
            #line 779 "..\..\..\..\MainWindow.xaml"
            this.TestPhoneApiButton.Click += new System.Windows.RoutedEventHandler(this.TestPhoneApiButton_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.SavePhoneApiButton = ((System.Windows.Controls.Button)(target));
            
            #line 787 "..\..\..\..\MainWindow.xaml"
            this.SavePhoneApiButton.Click += new System.Windows.RoutedEventHandler(this.SavePhoneApiButton_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            this.EmailGoogleRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 832 "..\..\..\..\MainWindow.xaml"
            this.EmailGoogleRadio.Checked += new System.Windows.RoutedEventHandler(this.EmailModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 31:
            this.EmailMicrosoftRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 837 "..\..\..\..\MainWindow.xaml"
            this.EmailMicrosoftRadio.Checked += new System.Windows.RoutedEventHandler(this.EmailModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 32:
            this.EmailManualModeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 841 "..\..\..\..\MainWindow.xaml"
            this.EmailManualModeRadio.Checked += new System.Windows.RoutedEventHandler(this.EmailModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 33:
            this.EmailModeDescriptionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 34:
            this.CaptchaYesModeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 909 "..\..\..\..\MainWindow.xaml"
            this.CaptchaYesModeRadio.Checked += new System.Windows.RoutedEventHandler(this.CaptchaYesModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 35:
            this.CaptchaCloudModeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 916 "..\..\..\..\MainWindow.xaml"
            this.CaptchaCloudModeRadio.Checked += new System.Windows.RoutedEventHandler(this.CaptchaCloudModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 36:
            this.CaptchaManualModeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 922 "..\..\..\..\MainWindow.xaml"
            this.CaptchaManualModeRadio.Checked += new System.Windows.RoutedEventHandler(this.CaptchaManualModeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 37:
            this.CaptchaModeDescriptionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 38:
            this.StartButton = ((System.Windows.Controls.Button)(target));
            
            #line 980 "..\..\..\..\MainWindow.xaml"
            this.StartButton.Click += new System.Windows.RoutedEventHandler(this.StartButton_Click);
            
            #line default
            #line hidden
            return;
            case 39:
            this.ContinueButton = ((System.Windows.Controls.Button)(target));
            
            #line 991 "..\..\..\..\MainWindow.xaml"
            this.ContinueButton.Click += new System.Windows.RoutedEventHandler(this.ContinueButton_Click);
            
            #line default
            #line hidden
            return;
            case 40:
            this.PauseButton = ((System.Windows.Controls.Button)(target));
            
            #line 1002 "..\..\..\..\MainWindow.xaml"
            this.PauseButton.Click += new System.Windows.RoutedEventHandler(this.PauseButton_Click);
            
            #line default
            #line hidden
            return;
            case 41:
            this.StopButton = ((System.Windows.Controls.Button)(target));
            
            #line 1013 "..\..\..\..\MainWindow.xaml"
            this.StopButton.Click += new System.Windows.RoutedEventHandler(this.StopButton_Click);
            
            #line default
            #line hidden
            return;
            case 42:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 43:
            this.BrowserModeIndicator = ((System.Windows.Controls.Border)(target));
            return;
            case 44:
            this.BrowserModeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 45:
            this.UnprocessedCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 46:
            this.PendingCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 47:
            this.ProcessingCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 48:
            this.CompletedCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 49:
            this.FailedCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 50:
            this.DataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

