using Microsoft.Playwright;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AWS自动注册工具.Services
{
    /// <summary>
    /// 多语言元素定位服务
    /// 提供跨语言的元素定位方法，优先使用稳定属性，回退到多语言文本
    /// </summary>
    public class MultiLanguageElementService
    {
        private readonly IPage _page;

        public MultiLanguageElementService(IPage page)
        {
            _page = page;
        }

        /// <summary>
        /// 多语言文本映射表
        /// </summary>
        private static readonly Dictionary<string, string[]> TextMappings = new()
        {
            // AWS密钥创建相关
            ["create_access_key"] = new[] { "创建访问密钥", "Create access key", "Créer une clé d'accès" },
            ["done"] = new[] { "已完成", "Done", "Terminé" },
            ["continue"] = new[] { "继续", "Continue", "Continuer" },
            ["show"] = new[] { "显示", "Show", "Afficher" },
            ["hide"] = new[] { "隐藏", "Hide", "Masquer" },
            ["actions"] = new[] { "操作", "Actions", "Actions" },
            ["delete"] = new[] { "删除", "Delete", "Supprimer" },
            
            // MFA相关
            ["assign_mfa_device"] = new[] { "分配 MFA 设备", "Assign MFA device", "Assigner un appareil MFA" },
            ["device_name"] = new[] { "设备名称", "Device name", "Nom de l'appareil" },
            ["show_key"] = new[] { "显示密钥", "Show key", "Afficher la clé" },
            ["add_mfa"] = new[] { "添加 MFA", "Add MFA", "Ajouter MFA" },
            
            // IAM相关
            ["edit_iam_access"] = new[] { "编辑 IAM 访问", "Edit IAM access", "Modifier l'accès IAM" },
            ["activate_iam_access"] = new[] { "激活 IAM 访问权限", "Activate IAM access", "Activer l'accès IAM" },
            ["update"] = new[] { "更新", "Update", "Mettre à jour" },
            
            // 通用
            ["more"] = new[] { "更多", "More", "Plus" },
            ["security_credentials"] = new[] { "安全凭证", "Security credentials", "Informations d'identification de sécurité" },
            
            // 确认文本
            ["root_access_key_warning"] = new[] { 
                "我知道创建根访问密钥不是最佳实践，但我仍然想创建一个。",
                "I understand that root access keys are not recommended, but I want to create one.",
                "Je comprends que les clés d'accès root ne sont pas recommandées, mais je veux en créer une."
            }
        };

        /// <summary>
        /// 通过TestId或多语言文本定位创建访问密钥按钮
        /// </summary>
        public async Task<ILocator> GetCreateAccessKeyButton()
        {
            // 优先使用TestId
            try
            {
                var testIdButton = _page.GetByTestId("create-access-key");
                await testIdButton.WaitForAsync(new() { Timeout = 2000 });
                return testIdButton;
            }
            catch
            {
                // 回退到多语言文本定位
                return await GetElementByMultiLanguageText("create_access_key", AriaRole.Button);
            }
        }

        /// <summary>
        /// 通过稳定属性定位确认复选框
        /// </summary>
        public async Task<ILocator> GetConfirmCheckbox()
        {
            // 优先使用id属性
            try
            {
                var idCheckbox = _page.Locator("#ack-risk");
                await idCheckbox.WaitForAsync(new() { Timeout = 2000 });
                return idCheckbox;
            }
            catch
            {
                // 回退到name属性
                try
                {
                    var nameCheckbox = _page.Locator("[name='ack-risk']");
                    await nameCheckbox.WaitForAsync(new() { Timeout = 2000 });
                    return nameCheckbox;
                }
                catch
                {
                    // 最后回退到多语言文本定位
                    return await GetElementByMultiLanguageText("root_access_key_warning", AriaRole.Checkbox);
                }
            }
        }

        /// <summary>
        /// 通过稳定属性定位访问密钥单选按钮
        /// </summary>
        public async Task<ILocator> GetAccessKeyRadioButton()
        {
            // 使用name属性定位
            try
            {
                var radioButton = _page.Locator("[name='access-key-radio']");
                await radioButton.WaitForAsync(new() { Timeout = 5000 });
                return radioButton;
            }
            catch
            {
                // 如果name属性失效，通过表格行定位
                var accessKeyRows = await _page.Locator("tr").AllAsync();
                foreach (var row in accessKeyRows)
                {
                    var radioButtons = await row.Locator("input[type='radio']").AllAsync();
                    if (radioButtons.Count > 0)
                    {
                        return radioButtons[0];
                    }
                }
                throw new Exception("无法找到访问密钥单选按钮");
            }
        }

        /// <summary>
        /// 通过多语言文本定位"已完成"按钮
        /// </summary>
        public async Task<ILocator> GetDoneButton()
        {
            return await GetElementByMultiLanguageText("done", AriaRole.Button);
        }

        /// <summary>
        /// 通过多语言文本定位"操作"按钮
        /// </summary>
        public async Task<ILocator> GetActionsButton()
        {
            return await GetElementByMultiLanguageText("actions", AriaRole.Button);
        }

        /// <summary>
        /// 通过TestId或多语言文本定位显示按钮
        /// </summary>
        public async Task<ILocator> GetShowButton()
        {
            // 优先使用TestId
            try
            {
                var testIdButton = _page.GetByTestId("show-password-link");
                await testIdButton.WaitForAsync(new() { Timeout = 2000 });
                return testIdButton;
            }
            catch
            {
                // 回退到多语言文本定位
                return await GetElementByMultiLanguageText("show", AriaRole.Button);
            }
        }

        /// <summary>
        /// 通过多语言文本定位MFA相关按钮
        /// </summary>
        public async Task<ILocator> GetAssignMfaDeviceButton()
        {
            return await GetElementByMultiLanguageText("assign_mfa_device", AriaRole.Button);
        }

        public async Task<ILocator> GetShowKeyButton()
        {
            return await GetElementByMultiLanguageText("show_key", AriaRole.Button);
        }

        public async Task<ILocator> GetAddMfaButton()
        {
            return await GetElementByMultiLanguageText("add_mfa", AriaRole.Button);
        }

        /// <summary>
        /// 通过多语言文本定位IAM相关元素
        /// </summary>
        public async Task<ILocator> GetActivateIamAccessCheckbox()
        {
            return await GetElementByMultiLanguageText("activate_iam_access", AriaRole.Checkbox);
        }

        /// <summary>
        /// 通过多语言文本定位元素的通用方法
        /// </summary>
        private async Task<ILocator> GetElementByMultiLanguageText(string key, AriaRole role)
        {
            if (!TextMappings.ContainsKey(key))
            {
                throw new ArgumentException($"未找到文本映射: {key}");
            }

            var texts = TextMappings[key];
            Exception lastException = null;

            foreach (var text in texts)
            {
                try
                {
                    ILocator element;
                    if (role == AriaRole.Checkbox)
                    {
                        element = _page.GetByLabel(text);
                    }
                    else
                    {
                        element = _page.GetByRole(role, new() { Name = text });
                    }
                    
                    await element.WaitForAsync(new() { Timeout = 1000 });
                    return element;
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    continue;
                }
            }

            throw new Exception($"无法通过多语言文本定位元素 {key}: {lastException?.Message}");
        }

        /// <summary>
        /// 检测当前页面语言
        /// </summary>
        public async Task<string> DetectPageLanguage()
        {
            try
            {
                var htmlLang = await _page.GetAttributeAsync("html", "lang");
                if (!string.IsNullOrEmpty(htmlLang))
                {
                    return htmlLang.ToLower();
                }

                // 通过页面内容检测语言
                var pageContent = await _page.ContentAsync();
                if (pageContent.Contains("创建访问密钥") || pageContent.Contains("安全凭证"))
                {
                    return "zh-cn";
                }
                else if (pageContent.Contains("Create access key") || pageContent.Contains("Security credentials"))
                {
                    return "en-us";
                }
                else
                {
                    return "unknown";
                }
            }
            catch
            {
                return "unknown";
            }
        }
    }
}
